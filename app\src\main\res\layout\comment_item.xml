<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layoutDirection="ltr">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:background="@color/white"
        android:visibility="visible"
        tools:ignore="MissingConstraints">

        <RelativeLayout
            android:layout_width="wrap_content"

            android:layout_height="wrap_content">

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/iv_thumb"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_margin="8dp"
                android:scaleType="fitXY"
                app:riv_border_color="@color/white"
                app:riv_border_width="2dp"
                app:riv_corner_radius="30dip"
                app:riv_mutate_background="true"
                app:riv_oval="true"
                app:riv_tile_mode="clamp" />

            <TextView
                android:id="@+id/txt_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:alpha="0.1"
                android:fontFamily="@font/public_medium"
                android:gravity="center_vertical"
                android:singleLine="true"
                android:textAllCaps="true"
                android:textSize="40dp"
                android:textStyle="bold"
                android:visibility="gone" />
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:minHeight="80dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txt_user_name"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="5dp"
                android:fontFamily="@font/public_bold"
                android:gravity="center_vertical"
                android:singleLine="true"
                android:text="Zoney Micels"
                android:textColor="@color/font_black"
                android:textIsSelectable="true"
                android:textSize="14dp" />

            <TextView
                android:id="@+id/txt_comment"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="5dp"
                android:fontFamily="@font/public_medium"
                android:gravity="center_vertical"
                android:maxLines="4"
                android:textColor="@color/font_dark"
                android:textSize="12dp" />

            <TextView
                android:id="@+id/txt_date"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="10dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:fontFamily="@font/public_regular"
                android:gravity="center_vertical|end"
                android:singleLine="true"
                android:textColor="@color/font_dark"
                android:textSize="11dp" />


        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>