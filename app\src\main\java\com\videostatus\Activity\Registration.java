package com.videostatus.Activity;

import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.videostatus.Model.RegistrationModel.RegistrationModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class Registration extends AppCompatActivity {

    TextView txt_registration, txt_signup;
    EditText et_fullname, et_lastname, et_email, et_password, et_phone;

    String str_fullname, str_lastname, str_email, str_password, str_phone;

    ProgressDialog progressDialog;
    private PrefManager prefManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.registration);

        InitValue();

        txt_registration.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(
                        Registration.this, LoginActivity.class));
            }
        });

        txt_signup.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                str_fullname = et_fullname.getText().toString();
                str_lastname = et_lastname.getText().toString();
                str_email = et_email.getText().toString();
                str_password = et_password.getText().toString();
                str_phone = et_phone.getText().toString();

                if (TextUtils.isEmpty(str_fullname)) {
                    Toast.makeText(Registration.this, "Enter FirstName", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (TextUtils.isEmpty(str_lastname)) {
                    Toast.makeText(Registration.this, "Enter LastName", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (TextUtils.isEmpty(str_email)) {
                    Toast.makeText(Registration.this, "Enter Email Address", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (TextUtils.isEmpty(str_password)) {
                    Toast.makeText(Registration.this, "Enter Password", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (TextUtils.isEmpty(str_phone)) {
                    Toast.makeText(Registration.this, "Enter Phone Number", Toast.LENGTH_SHORT).show();
                    return;
                }
                SignUp();
            }
        });
    }


    public void InitValue() {
        prefManager = new PrefManager(this);
        progressDialog = new ProgressDialog(Registration.this);
        progressDialog.setMessage("Please wait...");
        progressDialog.setCanceledOnTouchOutside(false);

        txt_registration = (TextView) findViewById(R.id.txt_registration);
        et_fullname = (EditText) findViewById(R.id.et_fullname);
        et_lastname = (EditText) findViewById(R.id.et_lastname);
        et_email = (EditText) findViewById(R.id.et_email);
        et_password = (EditText) findViewById(R.id.et_password);
        et_phone = (EditText) findViewById(R.id.et_phone);
        txt_signup = (TextView) findViewById(R.id.txt_signup);

    }


    public void SignUp() {
        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<RegistrationModel> call = bookNPlayAPI.SignUp(str_fullname, str_lastname,
                str_email, str_password, str_phone);
        call.enqueue(new Callback<RegistrationModel>() {
            @Override
            public void onResponse(Call<RegistrationModel> call, Response<RegistrationModel> response) {
                Log.e("==>", "" + response.body());
                if (response.code() == 200) {
                    progressDialog.hide();
                    if (response.body().getStatus() == 200) {
                        Log.e("email==>", "" + response.body());

                        prefManager.setFirstTimeLaunch(false);
                        prefManager.setLoginId(response.body().getResult().getId());

                        startActivity(new Intent(Registration.this, MainActivity.class));
                        Registration.this.finish();
                    } else {
                        new AlertDialog.Builder(Registration.this)
                                .setTitle("" + getResources().getString(R.string.app_name))
                                .setMessage("" + response.body().getMessage())
                                .setCancelable(false)
                                .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialog, int which) {
                                        // Whatever...
                                    }
                                }).show();
                    }
                }
            }

            @Override
            public void onFailure(Call<RegistrationModel> call, Throwable t) {
                progressDialog.hide();
            }
        });
    }

}
