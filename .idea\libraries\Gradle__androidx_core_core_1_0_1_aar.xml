<component name="libraryTable">
  <library name="Gradle: androidx.core:core:1.0.1@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/457e69d7f74105c1590dfd6618ad4ae9/core-1.0.1/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/457e69d7f74105c1590dfd6618ad4ae9/core-1.0.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/457e69d7f74105c1590dfd6618ad4ae9/core-1.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/457e69d7f74105c1590dfd6618ad4ae9/core-1.0.1/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.core/core/1.0.1/ae10d2e1965b944830c2c2aaf154765ceb32e0bc/core-1.0.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>