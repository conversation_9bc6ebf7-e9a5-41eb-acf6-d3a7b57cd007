<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="AndExoPlayerView">
        <attr name="andexo_resize_mode" />
        <attr name="andexo_aspect_ratio" />
        <attr name="andexo_full_screen" format="boolean" />
        <attr name="andexo_play_when_ready" format="boolean" />
        <attr name="andexo_show_controller" format="boolean" />
    </declare-styleable>

    <attr name="andexo_resize_mode" format="enum">
        <enum name="Fit" value="1" />
        <enum name="Fill" value="2" />
        <enum name="Zoom" value="3" />
    </attr>

    <attr name="andexo_aspect_ratio" format="enum">
        <enum name="aspect_1_1" value="1" />
        <enum name="aspect_16_9" value="2" />
        <enum name="aspect_4_3" value="3" />
        <enum name="aspect_match" value="4" />
        <enum name="aspect_mp3" value="5" />
    </attr>

</resources>