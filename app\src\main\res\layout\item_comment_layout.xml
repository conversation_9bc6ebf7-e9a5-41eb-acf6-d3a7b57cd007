<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/mainlayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:orientation="vertical" />

    <RelativeLayout
        android:id="@+id/upperlayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp">

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/user_pic"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:src="@drawable/profile_image_placeholder" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/user_pic"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toLeftOf="@+id/action"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/username"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="Username"
                    android:textColor="@color/black"
                    android:textSize="13dp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="5dp"
                    android:text="Comment message here..."
                    android:textColor="@color/black"
                    android:textSize="12dp" />

            </LinearLayout>
        </RelativeLayout>
    </RelativeLayout>
</RelativeLayout>