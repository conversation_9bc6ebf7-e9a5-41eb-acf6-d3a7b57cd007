package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.iarcuschin.simpleratingbar.SimpleRatingBar;
import com.squareup.picasso.Picasso;
import com.videostatus.Activity.HomeSingalVideo;
import com.videostatus.Model.VideoModel.Result;
import com.videostatus.R;
import com.videostatus.Utility.Constant;

import java.util.List;

public class CategoryVideoAdapter extends RecyclerView.Adapter<CategoryVideoAdapter.MyViewHolder> {

    private List<Result> CategoryList;
    Context mcontext;
    String from;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        TextView txt_title, txt_bookmark,txt_view,txt_avg;
        ImageView iv_thumb;
        CardView cardview;
        SimpleRatingBar ratingbar;

        public MyViewHolder(View view) {
            super(view);
            txt_bookmark = (TextView) view.findViewById(R.id.txt_bookmark);
            iv_thumb = (ImageView) view.findViewById(R.id.iv_thumb);
            txt_title = (TextView) view.findViewById(R.id.txt_title);
            txt_view = (TextView) view.findViewById(R.id.txt_view);
            txt_avg= (TextView) view.findViewById(R.id.txt_avg);
            cardview = (CardView) view.findViewById(R.id.cardview);
            ratingbar=view.findViewById(R.id.ratingbar);
        }
    }


    public CategoryVideoAdapter(Context context, List<Result> moviesList, String from) {
        this.CategoryList = moviesList;
        this.mcontext = context;
        this.from = from;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView;
        if (from.equalsIgnoreCase("Home")) {
            itemView = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.cat_video_status, parent, false);
            return new CategoryVideoAdapter.MyViewHolder(itemView);
        } else {
            itemView = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.cat_video_status_grid, parent, false);
            return new CategoryVideoAdapter.MyViewHolder(itemView);
        }
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {
        holder.txt_title.setText(CategoryList.get(position).getVideoTitle());
        holder.txt_view.setText(withSuffix(Long.parseLong(""+CategoryList.get(position).getView()))+"");

        Picasso.with(mcontext).load(CategoryList.get(position).getThumbnailImg()).resize(400, 400)
                .placeholder(R.drawable.no_image).centerInside().into(holder.iv_thumb);

        holder.ratingbar.setRating(Float.parseFloat(CategoryList.get(position).getAvgRating()));
        holder.txt_avg.setText(" "+CategoryList.get(position).getAvgRating());

        holder.txt_bookmark.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
            }
        });

        holder.cardview.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Constant.VideoList = CategoryList;
                Intent intent = new Intent(mcontext, HomeSingalVideo.class);
                intent.putExtra("position", position);
                mcontext.startActivity(intent);
            }
        });

    }

    @Override
    public int getItemCount() {
        return CategoryList.size();
    }


    public String withSuffix(long count) {
        if (count < 1000) return "" + count;
        int exp = (int) (Math.log(count) / Math.log(1000));
        return String.format("%.1f %c",
                count / Math.pow(1000, exp),
                "kMGTPE".charAt(exp-1));
    }
}
