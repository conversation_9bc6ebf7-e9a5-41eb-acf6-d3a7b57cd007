
package com.videostatus.Model.SettingModel;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class Result {

    @SerializedName("host_email")
    @Expose
    private String hostEmail;
    @SerializedName("app_name")
    @Expose
    private String appName;
    @SerializedName("app_desripation")
    @Expose
    private String appDesripation;
    @SerializedName("app_logo")
    @Expose
    private String appLogo;
    @SerializedName("app_version")
    @Expose
    private String appVersion;
    @SerializedName("Author")
    @Expose
    private String author;
    @SerializedName("contact")
    @Expose
    private String contact;
    @SerializedName("email")
    @Expose
    private String email;
    @SerializedName("website")
    @Expose
    private String website;
    @SerializedName("privacy_policy")
    @Expose
    private String privacyPolicy;
    @SerializedName("publisher_id")
    @Expose
    private String publisherId;
    @SerializedName("banner_ad")
    @Expose
    private String bannerAd;
    @SerializedName("banner_adid")
    @Expose
    private String bannerAdid;
    @SerializedName("interstital_ad")
    @Expose
    private String interstitalAd;
    @SerializedName("interstital_adid")
    @Expose
    private String interstitalAdid;
    @SerializedName("interstital_adclick")
    @Expose
    private String interstitalAdclick;
    @SerializedName("onesignal_apid")
    @Expose
    private String onesignalApid;
    @SerializedName("onesignal_rest_key")
    @Expose
    private String onesignalRestKey;

    public String getHostEmail() {
        return hostEmail;
    }

    public void setHostEmail(String hostEmail) {
        this.hostEmail = hostEmail;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppDesripation() {
        return appDesripation;
    }

    public void setAppDesripation(String appDesripation) {
        this.appDesripation = appDesripation;
    }

    public String getAppLogo() {
        return appLogo;
    }

    public void setAppLogo(String appLogo) {
        this.appLogo = appLogo;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getPrivacyPolicy() {
        return privacyPolicy;
    }

    public void setPrivacyPolicy(String privacyPolicy) {
        this.privacyPolicy = privacyPolicy;
    }

    public String getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(String publisherId) {
        this.publisherId = publisherId;
    }

    public String getBannerAd() {
        return bannerAd;
    }

    public void setBannerAd(String bannerAd) {
        this.bannerAd = bannerAd;
    }

    public String getBannerAdid() {
        return bannerAdid;
    }

    public void setBannerAdid(String bannerAdid) {
        this.bannerAdid = bannerAdid;
    }

    public String getInterstitalAd() {
        return interstitalAd;
    }

    public void setInterstitalAd(String interstitalAd) {
        this.interstitalAd = interstitalAd;
    }

    public String getInterstitalAdid() {
        return interstitalAdid;
    }

    public void setInterstitalAdid(String interstitalAdid) {
        this.interstitalAdid = interstitalAdid;
    }

    public String getInterstitalAdclick() {
        return interstitalAdclick;
    }

    public void setInterstitalAdclick(String interstitalAdclick) {
        this.interstitalAdclick = interstitalAdclick;
    }

    public String getOnesignalApid() {
        return onesignalApid;
    }

    public void setOnesignalApid(String onesignalApid) {
        this.onesignalApid = onesignalApid;
    }

    public String getOnesignalRestKey() {
        return onesignalRestKey;
    }

    public void setOnesignalRestKey(String onesignalRestKey) {
        this.onesignalRestKey = onesignalRestKey;
    }

}
