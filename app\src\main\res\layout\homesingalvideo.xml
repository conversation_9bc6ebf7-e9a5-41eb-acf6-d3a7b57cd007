<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_latest"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black" />

        <ProgressBar
            android:id="@+id/p_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:layout_alignParentBottom="true"
            android:indeterminateOnly="true"
            android:indeterminateTint="@color/colorPrimaryDark"
            android:secondaryProgress="@android:integer/config_shortAnimTime"
            android:visibility="visible" />

    </RelativeLayout>

</FrameLayout>
