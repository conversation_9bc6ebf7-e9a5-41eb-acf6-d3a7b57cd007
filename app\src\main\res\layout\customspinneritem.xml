<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:layout_margin="10dp">

    <TextView
        android:id="@+id/txt_cat_name"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fontFamily="@font/public_medium"
        android:gravity="center_vertical"
        android:paddingLeft="10dp"
        android:text="TextView"
        android:textSize="14dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"></TextView>

</androidx.constraintlayout.widget.ConstraintLayout>