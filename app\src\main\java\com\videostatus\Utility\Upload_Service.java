package com.videostatus.Utility;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Binder;
import android.os.IBinder;
import android.util.Base64;
import android.util.Log;

import androidx.annotation.Nullable;

import com.videostatus.Activity.MainActivity;
import com.videostatus.Interface.ServiceCallback;
import com.videostatus.Model.UploadVideoModel.UploadVideoModel;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;


// this the background service which will upload the video into database
public class Upload_Service extends Service {

    private final IBinder mBinder = new LocalBinder();

    boolean mAllowRebind;
    ServiceCallback Callback;

    File file;
    String draft_file, duet_video_id;
    String videopath;
    String description;
    String privacy_type;
    String allow_comment, allow_duet;
    Uri uri;
    SharedPreferences sharedPreferences;
    PrefManager prefManager;
    File file_thumbnail;
    String Gif_base_64 = "";

    public Upload_Service() {
        super();
    }

    public Upload_Service(ServiceCallback serviceCallback) {
        Callback = serviceCallback;
    }

    public void setCallbacks(ServiceCallback serviceCallback) {
        Callback = serviceCallback;
    }

    public class LocalBinder extends Binder {
        public Upload_Service getService() {
            return Upload_Service.this;
        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return mBinder;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        return mAllowRebind;
    }

    @Override
    public void onCreate() {
        sharedPreferences = getSharedPreferences(Constant.pref_name, MODE_PRIVATE);
        prefManager = new PrefManager(getApplicationContext());
    }


    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {

        if (intent != null) {
            if (intent.getAction().equals("startservice")) {
                showNotification();

                videopath = intent.getStringExtra("uri");
                uri = Uri.parse(videopath);
                draft_file = intent.getStringExtra("draft_file");
                duet_video_id = intent.getStringExtra("duet_video_id");
                description = intent.getStringExtra("desc");
                privacy_type = intent.getStringExtra("privacy_type");
                allow_comment = intent.getStringExtra("allow_comment");
                allow_duet = intent.getStringExtra("allow_duet");
                file = new File(intent.getStringExtra("str_image"));

                Log.e("description", "" + description);
                Log.e("description==>", "" + prefManager.getValue("desc"));

                new Thread(new Runnable() {
                    @Override
                    public void run() {

//                        thumb_base_64 = prefManager.getValue(Constant.uploading_video_thumb);
                        Log.e("thumb_base_64", "" + file);

                        File myVideo = new File(uri.getPath());

                        Call<UploadVideoModel> call;

                        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();

                        /*Thumbnail*/
                        RequestBody requestFile = RequestBody.create(MediaType.parse("image/*"), file);
                        MultipartBody.Part body =
                                MultipartBody.Part.createFormData("picbase64", file.getName(), requestFile);

                        /*Video*/
                        RequestBody requestFile1 = RequestBody.create(MediaType.parse("video/*"), myVideo);
                        MultipartBody.Part body1 =
                                MultipartBody.Part.createFormData("videobase64", myVideo.getName(), requestFile1);

                        RequestBody fb_id = RequestBody.create(MediaType.parse("text/plain"), "" + prefManager.getLoginId());
                        RequestBody video_title = RequestBody.create(MediaType.parse("text/plain"), "" + prefManager.getValue("desc"));
                        RequestBody video_category = RequestBody.create(MediaType.parse("text/plain"), "");
                        RequestBody video_duration = RequestBody.create(MediaType.parse("text/plain"), "30");
                        RequestBody sound_id = RequestBody.create(MediaType.parse("text/plain"), Constant.Selected_sound_id);

                        call = bookNPlayAPI.upload_video(body, body1, fb_id, video_title, video_category,
                                video_duration, sound_id);

                        call.enqueue(new Callback<UploadVideoModel>() {
                            @Override
                            public void onResponse(Call<UploadVideoModel> call, Response<UploadVideoModel> response) {
                                Log.e("response", "" + response.body());
                                if (response.code() == 200) {
                                    Log.e("response", "" + response.body().getMessage());
                                } else {
                                    Log.e("response==>else", "" + response.body().getMessage());
                                }
                                Callback.ShowResponce(response.body().getMessage(), response.body().getStatus());
                                stopForeground(true);
                                stopSelf();
                            }

                            @Override
                            public void onFailure(Call<UploadVideoModel> call, Throwable t) {
                                Log.e("t", "onFailure" + t.getMessage());
                                Callback.ShowResponce("There is some kind of problem from Server side, Please Try again", 400);
                                stopForeground(true);
                                stopSelf();
                            }
                        });
                    }
                }).start();

            } else if (intent.getAction().equals("stopservice")) {
                stopForeground(true);
                stopSelf();
            }
        }
        return Service.START_STICKY;
    }

    // this will show the sticky notification during uploading video
    private void showNotification() {

        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0,
                notificationIntent, 0);

        final String CHANNEL_ID = "default";
        final String CHANNEL_NAME = "Default";

        NotificationManager notificationManager = (NotificationManager) this.getSystemService(this.NOTIFICATION_SERVICE);

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel defaultChannel = new NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_HIGH);
            notificationManager.createNotificationChannel(defaultChannel);
        }

        androidx.core.app.NotificationCompat.Builder builder = (androidx.core.app.NotificationCompat.Builder) new androidx.core.app.NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.stat_sys_upload)
                .setContentTitle("Uploading Video")
                .setContentText("Please wait! Video is uploading....")
                .setLargeIcon(BitmapFactory.decodeResource(this.getResources(),
                        android.R.drawable.stat_sys_upload))
                .setContentIntent(pendingIntent);

        Notification notification = builder.build();
        startForeground(101, notification);
    }

    // for video base64
    private String encodeFileToBase64Binary(Uri fileName)
            throws IOException {

        File file = new File(fileName.getPath());
        byte[] bytes = loadFile(file);
        String encodedString = Base64.encodeToString(bytes, Base64.DEFAULT);
        return encodedString;
    }

    private static byte[] loadFile(File file) throws IOException {
        InputStream is = new FileInputStream(file);

        long length = file.length();
        if (length > Integer.MAX_VALUE) {
            // File is too large
        }
        byte[] bytes = new byte[(int) length];

        int offset = 0;
        int numRead = 0;
        while (offset < bytes.length
                && (numRead = is.read(bytes, offset, bytes.length - offset)) >= 0) {
            offset += numRead;
        }

        if (offset < bytes.length) {
            throw new IOException("Could not completely read file " + file.getName());
        }

        is.close();
        return bytes;
    }

    public void Delete_draft_file() {
        try {
            if (draft_file != null) {
                File file = new File(draft_file);
                file.delete();
            }
        } catch (Exception e) {

        }
    }


}