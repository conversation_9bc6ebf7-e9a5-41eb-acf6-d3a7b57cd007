package com.videostatus.Fragment;

import android.app.AlertDialog;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.fragment.app.Fragment;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.widget.SwitchCompat;
import com.facebook.login.LoginManager;
import com.google.android.gms.auth.api.signin.GoogleSignIn;
import com.google.android.gms.auth.api.signin.GoogleSignInClient;
import com.google.android.gms.auth.api.signin.GoogleSignInOptions;
import com.onesignal.OneSignal;
import com.videostatus.Activity.AboutUs;
import com.videostatus.Activity.DownloadStatus;
import com.videostatus.Activity.LoginActivity;
import com.videostatus.Activity.MainActivity;
import com.videostatus.Activity.MyFavorite;
import com.videostatus.Activity.Privacypolicy;
import com.videostatus.Activity.Profile;
import com.videostatus.Activity.StatusDetails;
import com.videostatus.BuildConfig;
import com.videostatus.R;
import com.videostatus.Utility.LocaleUtils;
import com.videostatus.Utility.PrefManager;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class Settings extends Fragment {
    SwitchCompat switch_push;
    PrefManager prefManager;
    Spinner spinner;
    TextView txt_profile, txt_my_favorite, txt_about_us, txt_share_app,
            txt_rate_app, txt_login, txt_privacy_policy,my_downloaded_status;
    String currentLanguage = "en", currentLang;

    GoogleSignInClient mGoogleSignInClient;
    GoogleSignInOptions gso;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View root = inflater.inflate(R.layout.settings, container, false);

        MainActivity.al_title.setVisibility(View.VISIBLE);
        MainActivity.ly_bottom.setBackground(getResources().getDrawable(R.drawable.bottom_gra_bg));

        prefManager = new PrefManager(getActivity());
        spinner = root.findViewById(R.id.spinner);
        switch_push = (SwitchCompat) root.findViewById(R.id.switch_push);
        ImageView iv_clear = (ImageView) root.findViewById(R.id.iv_clear);

        txt_profile = (TextView) root.findViewById(R.id.txt_profile);
        txt_my_favorite = (TextView) root.findViewById(R.id.txt_my_favorite);
        my_downloaded_status = (TextView) root.findViewById(R.id.my_downloaded_status);
        txt_about_us = root.findViewById(R.id.txt_about_us);
        txt_share_app = (TextView) root.findViewById(R.id.txt_share_app);
        txt_rate_app = (TextView) root.findViewById(R.id.txt_rate_app);
        txt_login = (TextView) root.findViewById(R.id.txt_login);
        txt_privacy_policy = root.findViewById(R.id.txt_privacy_policy);

        if (prefManager.getBool("PUSH")) {
            switch_push.setChecked(true);
        } else {
            switch_push.setChecked(false);
        }

        gso = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN).requestEmail().build();
        mGoogleSignInClient = GoogleSignIn.getClient(getActivity(), gso);

        txt_profile.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (prefManager.getLoginId().equalsIgnoreCase("0")) {
                    startActivity(new Intent(getActivity(), LoginActivity.class));
                } else {
                    Intent intent = new Intent(getActivity(), Profile.class);
                    intent.putExtra("Id", "" + prefManager.getLoginId());
                    intent.putExtra("TO_Id", "" + prefManager.getLoginId());
                    startActivity(intent);
                }
            }
        });
        my_downloaded_status.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (prefManager.getLoginId().equalsIgnoreCase("0")) {
                    startActivity(new Intent(getActivity(), LoginActivity.class));
                } else {
                    startActivity(new Intent(getActivity(), DownloadStatus.class));
                }
            }
        });

        txt_my_favorite.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (prefManager.getLoginId().equalsIgnoreCase("0")) {
                    startActivity(new Intent(getActivity(), LoginActivity.class));
                } else {
                    startActivity(new Intent(getActivity(), MyFavorite.class));
                }
            }
        });

        switch_push.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    OneSignal.setSubscription(true);
                } else {
                    OneSignal.setSubscription(false);
                }
                prefManager.setBool("PUSH", isChecked);
            }
        });


        iv_clear.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String root = getActivity().getExternalCacheDir().getAbsolutePath();
                File file = new File(root);
                if (file.isDirectory()) {
                    String[] children = file.list();
                    for (String aChildren : children) {
                        new File(file, aChildren).delete();
                    }
                    Toast.makeText(getActivity(), getResources().getString(R.string.locally_cached_data), Toast.LENGTH_SHORT).show();
                }
            }
        });

        txt_privacy_policy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                startActivity(new Intent(getActivity(), Privacypolicy.class));
            }
        });
        txt_about_us.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(getActivity(), AboutUs.class));
            }
        });

        txt_share_app.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    Intent shareIntent = new Intent(Intent.ACTION_SEND);
                    shareIntent.setType("text/plain");
                    shareIntent.putExtra(Intent.EXTRA_SUBJECT, "" + getResources().getString(R.string.app_name));
                    String shareMessage = "\nLet me recommend you this application\n\n";
                    shareMessage = shareMessage + "https://play.google.com/store/apps/details?id=" + BuildConfig.APPLICATION_ID + "\n\n";
                    shareIntent.putExtra(Intent.EXTRA_TEXT, shareMessage);
                    startActivity(Intent.createChooser(shareIntent, "choose one"));
                } catch (Exception e) {
                    //e.toString();
                }
            }
        });

        txt_rate_app.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    startActivity(new Intent(Intent.ACTION_VIEW,
                            Uri.parse("market://details?id=" + getActivity().getPackageName())));
                } catch (android.content.ActivityNotFoundException e) {
                    startActivity(new Intent(Intent.ACTION_VIEW,
                            Uri.parse("http://play.google.com/store/apps/details?id=" + getActivity().getPackageName())));
                }
            }
        });

        if (!prefManager.getLoginId().equalsIgnoreCase("0"))
            txt_login.setText(getActivity().getResources().getString(R.string.logout));
        else
            txt_login.setText(getActivity().getResources().getString(R.string.login));

        txt_login.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (prefManager.getLoginId().equalsIgnoreCase("0")) {
                    startActivity(new Intent(getActivity(), LoginActivity.class));
                } else {
                    logout();
                }
            }
        });

        spinner_onclick();
        currentLanguage = prefManager.getValue("select_language");
        Log.e("lan_currentLan", "" + currentLanguage);

//        currentLanguage = prefManager.getValue("select_language");
        currentLanguage = LocaleUtils.getSelectedLanguageId();
        Log.e("currentLanguage", "" + currentLanguage);
        return root;
    }

    private void spinner_onclick() {
        List<String> list = new ArrayList<String>();
        list.add("English");
        list.add("Arabic");
        list.add("French");

        ArrayAdapter<String> adapter = new ArrayAdapter<String>(getActivity(), android.R.layout.simple_spinner_item, list);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setAdapter(adapter);

        if (LocaleUtils.getSelectedLanguageId().equalsIgnoreCase("en")) {
            Log.e("selected_eng", "english");
            spinner.setSelection(0);
        }
        if (LocaleUtils.getSelectedLanguageId().equalsIgnoreCase("ar")) {
            Log.e("select_Arabic", "Arabic");
            spinner.setSelection(1);
        }
        if (LocaleUtils.getSelectedLanguageId().equalsIgnoreCase("fr")) {
            Log.e("select_Franch", "fr");
            spinner.setSelection(2);
        }

        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long l) {
                Log.e("pos", "" + position);
                switch (position) {
                    case 0:
                        setLocale("en");
                        break;
                    case 1:
                        setLocale("ar");
                        break;
                    case 2:
                        setLocale("fr");
                        break;
                    default:
                        break;
                }

            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {

            }
        });
    }

    private void setLocale(String localeName) {
        try {
            Log.e("lan_name", "" + localeName);
            Log.e("currentLanguage2", "" + currentLanguage);
            if (!localeName.equals(currentLanguage)) {
                LocaleUtils.setSelectedLanguageId(localeName);
                Intent i = getActivity().getBaseContext().getPackageManager()
                        .getLaunchIntentForPackage(getActivity().getBaseContext().getPackageName());
                startActivity(i);
                getActivity().finish();
            } else {
//                Toast.makeText(getActivity(), "Language already selected!", Toast.LENGTH_SHORT).show();
            }

        } catch (Exception e) {
            Log.e("error_msg", "" + e.getMessage());
        }
    }

    public void logout() {
        new AlertDialog.Builder(new ContextThemeWrapper(getActivity(), R.style.AlertDialogDanger))
                .setIcon(android.R.drawable.ic_dialog_alert)
                .setTitle(getResources().getString(R.string.app_name))
                .setMessage("Are you sure you want to Logout?")
                .setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        prefManager.setLoginId("0");
                        LoginManager.getInstance().logOut();
                        mGoogleSignInClient.signOut();
                        Intent intent = new Intent(getActivity(), LoginActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                        startActivity(intent);
                        getActivity().finish();
                    }

                })
                .setNegativeButton("No", null)
                .show();
    }

}
