
package com.videostatus.Model.RewardModel;

import java.util.List;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class RewardModel {

    @SerializedName("status")
    @Expose
    private Integer status;
    @SerializedName("total_rewards")
    @Expose
    private Integer total_rewards;
    @SerializedName("result")
    @Expose
    private List<Result> result = null;
    @SerializedName("Message")
    @Expose
    private String message;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<Result> getResult() {
        return result;
    }

    public void setResult(List<Result> result) {
        this.result = result;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getTotal_rewards() {
        return total_rewards;
    }

    public void setTotal_rewards(Integer total_rewards) {
        this.total_rewards = total_rewards;
    }
}
