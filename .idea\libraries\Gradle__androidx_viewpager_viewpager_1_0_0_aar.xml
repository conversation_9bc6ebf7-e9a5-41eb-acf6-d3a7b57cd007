<component name="libraryTable">
  <library name="Gradle: androidx.viewpager:viewpager:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/ca9400a50a2feb447dbc5e144e7135fb/viewpager-1.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/ca9400a50a2feb447dbc5e144e7135fb/viewpager-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/ca9400a50a2feb447dbc5e144e7135fb/viewpager-1.0.0/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.viewpager/viewpager/1.0.0/db045f92188b9d247d5f556866f8861ab68528f0/viewpager-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>