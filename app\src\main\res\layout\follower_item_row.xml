<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="160dp"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:orientation="vertical"
        tools:ignore="MissingConstraints">

        <androidx.cardview.widget.CardView
            android:id="@+id/cardview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="5dp"
            android:visibility="visible"
            app:cardCornerRadius="5dp"
            app:cardElevation="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.makeramen.roundedimageview.RoundedImageView
                    android:id="@+id/iv_user_pic"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_marginStart="5dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitXY"
                    android:src="@color/white"
                    android:visibility="visible"
                    app:riv_border_color="@color/white"
                    app:riv_border_width="1dip"
                    app:riv_corner_radius="60dip"
                    app:riv_oval="false"
                    app:riv_tile_mode="clamp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:layout_marginLeft="5dp"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/txt_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/public_semibold"
                        android:gravity="center_vertical"
                        android:text="Seen Wilson"
                        android:singleLine="true"
                        android:textSize="@dimen/text_12"
                        android:textColor="@color/font_dark" />

                    <TextView
                        android:id="@+id/txt_points"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/public_semibold"
                        android:gravity="center"
                        android:text="101 PT"
                        android:textSize="@dimen/text_12"
                        android:textColor="@color/font_dark" />

                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>