package com.videostatus.Adapter;

import android.app.Activity;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.viewpager.widget.PagerAdapter;

import com.iarcuschin.simpleratingbar.SimpleRatingBar;
import com.squareup.picasso.Picasso;
import com.videostatus.Activity.HomeSingalVideo;
import com.videostatus.Activity.StatusDetails;
import com.videostatus.Model.VideoModel.Result;
import com.videostatus.R;
import com.videostatus.Utility.Constant;

import java.util.List;

public class BannerAdapter extends PagerAdapter {

    private LayoutInflater inflater;
    private Activity context;
    private List<Result> mBennerList;

    public BannerAdapter(Activity context, List<Result> itemChannels) {
        this.context = context;
        this.mBennerList = itemChannels;
        inflater = context.getLayoutInflater();

    }

    @Override
    public int getCount() {
        return mBennerList.size();
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view.equals(object);
    }

    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, final int position) {
        View imageLayout = inflater.inflate(R.layout.banner_item_row, container, false);
        assert imageLayout != null;

        SimpleRatingBar ratingbar=imageLayout.findViewById(R.id.ratingbar);
        ImageView imageView = imageLayout.findViewById(R.id.image);
        TextView title = imageLayout.findViewById(R.id.title);
        TextView txt_avg = imageLayout.findViewById(R.id.txt_avg);
        TextView view = imageLayout.findViewById(R.id.view);
        TextView txt_play = imageLayout.findViewById(R.id.txt_play);
        TextView txt_play2 = imageLayout.findViewById(R.id.txt_play2);
        CardView card_view=imageLayout.findViewById(R.id.card_view);

        Picasso.with(context).load(mBennerList.get(position).getThumbnailImg())
                .resize(400, 400)
                .placeholder(R.drawable.no_image).centerCrop().into(imageView);

        title.setText(""+mBennerList.get(position).getVideoTitle());
        ratingbar.setRating(Float.parseFloat(mBennerList.get(position).getAvgRating()));
        txt_avg.setText(" "+mBennerList.get(position).getAvgRating());
        view.setText(" "+withSuffix(Long.parseLong(""+mBennerList.get(position).getView()))+" Views | "
                +withSuffix(Long.parseLong(""+mBennerList.get(position).getTotalComment()))+" Comments");

        card_view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Constant.VideoList = mBennerList;
                Intent intent = new Intent(context, HomeSingalVideo.class);
                intent.putExtra("position", position);
                context.startActivity(intent);
            }
        });

        container.addView(imageLayout, 0);
        return imageLayout;
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        (container).removeView((View) object);
    }

    public String withSuffix(long count) {
        if (count < 1000) return "" + count;
        int exp = (int) (Math.log(count) / Math.log(1000));
        return String.format("%.1f %c",
                count / Math.pow(1000, exp),
                "kMGTPE".charAt(exp-1));
    }

}