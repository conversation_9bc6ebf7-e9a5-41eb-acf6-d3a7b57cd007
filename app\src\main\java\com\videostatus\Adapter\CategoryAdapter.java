package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.makeramen.roundedimageview.RoundedImageView;
import com.squareup.picasso.Picasso;
import com.videostatus.Activity.StatusDetails;
import com.videostatus.Activity.VideoList;
import com.videostatus.Model.CategoryModel.Result;
import com.videostatus.R;

import java.util.List;

import static com.squareup.picasso.Picasso.Priority.HIGH;

public class CategoryAdapter extends RecyclerView.Adapter<CategoryAdapter.MyViewHolder> {

    private List<Result> CategoryList;
    Context mcontext;
    String from;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        public TextView title;
        LinearLayout ly_main;
        ImageView iv_thumb;

        public MyViewHolder(View view) {
            super(view);
            title = view.findViewById(R.id.title);
            ly_main = view.findViewById(R.id.ly_main);
            iv_thumb = view.findViewById(R.id.iv_thumb);
        }
    }

    public CategoryAdapter(Context context, List<Result> moviesList, String from) {
        this.CategoryList = moviesList;
        this.mcontext = context;
        this.from = from;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView;
        if (from.equalsIgnoreCase("Home")) {
            itemView = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.cate_item_row, parent, false);
            return new CategoryAdapter.MyViewHolder(itemView);
        } else {
            itemView = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.cate_item_grid, parent, false);
            return new CategoryAdapter.MyViewHolder(itemView);
        }

    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        holder.title.setText(CategoryList.get(position).getCategoryName());

        Log.e("c_image", "" + CategoryList.get(position).getCategoryImage());

        Picasso.with(mcontext).load("" + CategoryList.get(position).getCategoryImage())
                .priority(HIGH).into(holder.iv_thumb);

        holder.ly_main.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Log.e("click", "call");
                Intent intent = new Intent(mcontext, VideoList.class);
                intent.putExtra("Id", CategoryList.get(position).getId());
                intent.putExtra("Name", CategoryList.get(position).getCategoryName());
                mcontext.startActivity(intent);
            }
        });
    }

    @Override
    public int getItemCount() {
        return CategoryList.size();
    }

}
