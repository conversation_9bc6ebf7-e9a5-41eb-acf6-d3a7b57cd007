<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="WizardSettings">
    <option name="children">
      <map>
        <entry key="imageWizard">
          <value>
            <PersistentState>
              <option name="children">
                <map>
                  <entry key="imageAssetPanel">
                    <value>
                      <PersistentState>
                        <option name="children">
                          <map>
                            <entry key="actionbar">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                  <option name="values">
                                    <map>
                                      <entry key="theme" value="HOLO_DARK" />
                                      <entry key="themeColor" value="ffffff" />
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="launcher">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="foregroundImage">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="scalingPercent" value="49" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                  <option name="values">
                                    <map>
                                      <entry key="backgroundAssetType" value="COLOR" />
                                      <entry key="backgroundColor" value="ffffff" />
                                      <entry key="foregroundImage" value="$PROJECT_DIR$/../../Design/New/app_icon/android/playstore-icon.png" />
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="launcherLegacy">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="notification">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                          </map>
                        </option>
                      </PersistentState>
                    </value>
                  </entry>
                </map>
              </option>
            </PersistentState>
          </value>
        </entry>
        <entry key="vectorWizard">
          <value>
            <PersistentState>
              <option name="children">
                <map>
                  <entry key="vectorAssetStep">
                    <value>
                      <PersistentState>
                        <option name="values">
                          <map>
                            <entry key="assetSourceType" value="FILE" />
                            <entry key="outputName" value="ic_create" />
                            <entry key="sourceFile" value="$USER_HOME$/Downloads/plus-sign.svg" />
                          </map>
                        </option>
                      </PersistentState>
                    </value>
                  </entry>
                </map>
              </option>
            </PersistentState>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>