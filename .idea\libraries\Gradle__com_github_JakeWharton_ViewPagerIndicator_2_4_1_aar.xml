<component name="libraryTable">
  <library name="Gradle: com.github.JakeWharton:ViewPagerIndicator:2.4.1@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/f5c1d607b0ed49eacb5ff2915633eca2/jetified-ViewPagerIndicator-2.4.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/f5c1d607b0ed49eacb5ff2915633eca2/jetified-ViewPagerIndicator-2.4.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/f5c1d607b0ed49eacb5ff2915633eca2/jetified-ViewPagerIndicator-2.4.1/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.JakeWharton/ViewPagerIndicator/2.4.1/955ad71f0af3cf4bb60bb9bc252c2020c1af4aae/ViewPagerIndicator-2.4.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.JakeWharton/ViewPagerIndicator/2.4.1/10b5915b5f2eb2fe91ba6df68f99a3ded4e98178/ViewPagerIndicator-2.4.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>