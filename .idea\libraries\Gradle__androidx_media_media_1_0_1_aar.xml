<component name="libraryTable">
  <library name="Gradle: androidx.media:media:1.0.1@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/8bf60aba6e9eb9367f5005a4ed1fcb9e/media-1.0.1/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/8bf60aba6e9eb9367f5005a4ed1fcb9e/media-1.0.1/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/8bf60aba6e9eb9367f5005a4ed1fcb9e/media-1.0.1/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/8bf60aba6e9eb9367f5005a4ed1fcb9e/media-1.0.1/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.media/media/1.0.1/47b07b12e00d67295d73ffde730c759b2dfc4c11/media-1.0.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>