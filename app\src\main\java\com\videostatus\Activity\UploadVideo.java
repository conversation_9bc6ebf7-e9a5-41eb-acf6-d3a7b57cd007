package com.videostatus.Activity;

import android.Manifest;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.PorterDuff;
import android.media.MediaMetadataRetriever;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.videostatus.Adapter.CustomArrayAdapter;
import com.videostatus.Model.UploadVideoModel.UploadVideoModel;
import com.videostatus.R;
import com.videostatus.Utility.Constant;
import com.videostatus.Utility.PrefManager;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Calendar;
import java.util.concurrent.TimeUnit;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class UploadVideo extends AppCompatActivity {

    Spinner spinner1;
    PrefManager prefManager;
    String str_Selectcat, str_Selectcat_id, str_Video_Title;
    ProgressDialog progressDialog;

    String quote_image;
    ImageView iv_browse;
    TextView txt_browse_image, txt_browse_video, txt_browse_video_file, txt_submit;
    EditText et_video_title;

    private static final int MY_CAMERA_REQUEST_CODE = 100;
    int GALLERY = 11;
    int CAMERA = 111;
    int GALLERY_VIDEO = 1111;
    File file_thumbnail, file;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.upload);

        Toolbar toolbar = (Toolbar) findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Upload Status");

        toolbar.setTitleTextColor(getResources().getColor(R.color.colorPrimary));
        toolbar.getNavigationIcon().setColorFilter(getResources().getColor(R.color.colorPrimary), PorterDuff.Mode.SRC_ATOP);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowHomeEnabled(true);

        spinner1 = (Spinner) findViewById(R.id.spinner1);
        iv_browse = (ImageView) findViewById(R.id.iv_browse);

        txt_browse_image = (TextView) findViewById(R.id.txt_browse_image);
        txt_browse_video = (TextView) findViewById(R.id.txt_browse_video);
        txt_browse_video_file = (TextView) findViewById(R.id.txt_browse_video_file);
        txt_submit = (TextView) findViewById(R.id.txt_submit);
        et_video_title = (EditText) findViewById(R.id.et_video_title);

        prefManager = new PrefManager(UploadVideo.this);

        progressDialog = new ProgressDialog(UploadVideo.this);
        progressDialog.setMessage("Please wait...");
        progressDialog.setCanceledOnTouchOutside(false);

        CustomArrayAdapter adapter = new CustomArrayAdapter(this,
                R.layout.customspinneritem, prefManager.CatList);
        adapter.setDropDownViewResource(R.layout.customspinneritem);
        spinner1.setAdapter(adapter);
        spinner1.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            public void onItemSelected(AdapterView<?> parent, View view, int pos, long id) {
                str_Selectcat = ((TextView) view.findViewById(R.id.txt_cat_name)).getText().toString();
                Log.e("str_Selectcat", "" + str_Selectcat);
                for (int i = 0; i < prefManager.CatList.size(); i++) {
                    if (str_Selectcat.equalsIgnoreCase(prefManager.CatList.get(i).getCategoryName())) {
                        str_Selectcat_id = prefManager.CatList.get(i).getId();
                        Log.e("str_Selectcat_id", "" + str_Selectcat_id);
                    }
                }
            }

            public void onNothingSelected(AdapterView<?> parent) {
            }
        });

        txt_browse_image.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPictureDialog();
            }
        });

        txt_browse_video.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.e("==>", "Click");
                showVideoDialog();
            }
        });
        txt_submit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                    str_Video_Title = et_video_title.getText().toString();

                    if (TextUtils.isEmpty(str_Video_Title)) {
                        Toast.makeText(UploadVideo.this, "Enter Video Title", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    SetUploadquote();
                } else {
                    startActivity(new Intent(UploadVideo.this, LoginActivity.class));
                }

            }
        });

        Admob();
    }

    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    public void Admob() {

        try {
            AdView adView = (AdView) findViewById(R.id.adView);
            AdRequest adRequest = new AdRequest.Builder().build();
            adView.loadAd(adRequest);
            adView.setAdListener(new AdListener() {
                @Override
                public void onAdLoaded() {
                    // Code to be executed when an ad finishes loading.
                    Log.e("onAdLoaded=>", "onAdLoaded");
                }

                @Override
                public void onAdFailedToLoad(int errorCode) {
                    Log.e("onAdFailedToLoad=>", "" + errorCode);
                }

                @Override
                public void onAdOpened() {
                }

                @Override
                public void onAdLeftApplication() {
                }

                @Override
                public void onAdClosed() {
                }
            });
        } catch (Exception e) {
            Log.e("Exception=>", "" + e.getMessage());
        }
    }

    private void showPictureDialog() {
        AlertDialog.Builder pictureDialog = new AlertDialog.Builder(UploadVideo.this);
        pictureDialog.setTitle("Select Action");
        String[] pictureDialogItems = {
                "Select photo from gallery",
                "Capture photo from camera"};
        pictureDialog.setItems(pictureDialogItems,
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        switch (which) {
                            case 0:
                                choosePhotoFromGallary();
                                break;
                            case 1:
                                takePhotoFromCamera();
                                break;
                        }
                    }
                });
        pictureDialog.show();
    }

    private void showVideoDialog() {
        Log.e("==>", "showVideoDialog");
        chooseVideoFromGallary();
    }

    public void chooseVideoFromGallary() {
        Intent galleryIntent = new Intent(Intent.ACTION_PICK,
                android.provider.MediaStore.Video.Media.EXTERNAL_CONTENT_URI);
        startActivityForResult(galleryIntent, GALLERY_VIDEO);
    }

    public void choosePhotoFromGallary() {
        Intent galleryIntent = new Intent(Intent.ACTION_PICK,
                android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI);

        startActivityForResult(galleryIntent, GALLERY);
    }

    private void takePhotoFromCamera() {

        if (!isReadStorageAllowed()) {
            requestStoragePermission();
        } else {
            Intent intent = new Intent(android.provider.MediaStore.ACTION_IMAGE_CAPTURE);
            startActivityForResult(intent, CAMERA);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {

        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_CANCELED) {
            return;
        }
        if (requestCode == GALLERY) {
            if (data != null) {
                Uri contentURI = data.getData();
                file_thumbnail = new File(getRealPathFromURI(UploadVideo.this, contentURI));
                Log.e("file", "" + file_thumbnail);
                try {
                    Bitmap bitmap = MediaStore.Images.Media.getBitmap(UploadVideo.this.getContentResolver(), contentURI);
//                    file = saveImage(bitmap);
                    iv_browse.setImageBitmap(bitmap);

                    int nh = (int) (bitmap.getHeight() * (512.0 / bitmap.getWidth()));
                    bitmap = Bitmap.createScaledBitmap(bitmap, 512, nh, true);
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream);
                    byte[] byteArray = byteArrayOutputStream.toByteArray();
                    quote_image = Base64.encodeToString(byteArray, Base64.DEFAULT);
                    Log.e("quote_image", "" + quote_image);
                } catch (IOException e) {
                    e.printStackTrace();
                    Log.e("IOException", "" + e.getMessage());
                    Toast.makeText(UploadVideo.this, "Failed!", Toast.LENGTH_SHORT).show();
                }
            }
        } else if (requestCode == CAMERA) {
            Bitmap thumbnail = (Bitmap) data.getExtras().get("data");
            iv_browse.setImageBitmap(thumbnail);
            saveImage(thumbnail);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            thumbnail.compress(Bitmap.CompressFormat.PNG, 80, byteArrayOutputStream);
            byte[] byteArray = byteArrayOutputStream.toByteArray();
            quote_image = Base64.encodeToString(byteArray, Base64.DEFAULT);
            Log.e("quote_image", "" + quote_image);
        } else if (requestCode == GALLERY_VIDEO) {
            Uri contentURI = data.getData();

            String selectedVideoPath = getPath(contentURI);
            Log.e("path", selectedVideoPath);

            file = new File(getRealPathFromURI(UploadVideo.this, contentURI));
            Log.e("file", "" + file);

            long sizeInBytes = file.length();
            long sizeInMb = sizeInBytes / (1024 * 1024);
            Log.e("sizeInMb ", "" + sizeInMb);

            MediaMetadataRetriever retriever = new MediaMetadataRetriever();
            retriever.setDataSource(UploadVideo.this, contentURI);
            String time = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            long timeInMillisec = TimeUnit.MILLISECONDS.toSeconds(Long.parseLong(time));
            Log.e("timeInMillisec ", "" + timeInMillisec);
            retriever.release();

            if (timeInMillisec > 30) {
                txt_browse_video_file.setText("Please select 30 second or less video file");
            } else if (sizeInMb > 15) {
                txt_browse_video_file.setText("Please select 15 mb or less video file");
            } else {
                txt_browse_video_file.setText("" + file);
                saveVideoToInternalStorage(selectedVideoPath);
            }
        }
    }

    public String getPath(Uri uri) {
        String[] projection = {MediaStore.Video.Media.DATA};
        Cursor cursor = getContentResolver().query(uri, projection, null, null, null);
        if (cursor != null) {
            int column_index = cursor
                    .getColumnIndexOrThrow(MediaStore.Video.Media.DATA);
            cursor.moveToFirst();
            return cursor.getString(column_index);
        } else
            return null;
    }

    private void saveVideoToInternalStorage(String filePath) {

        File newfile;
        try {
            File currentFile = new File(filePath);
            File wallpaperDirectory = new File(Environment.getExternalStorageDirectory() + getResources().getString(R.string.app_name) + "/Video");
            newfile = new File(wallpaperDirectory, Calendar.getInstance().getTimeInMillis() + ".mp4");
            if (!wallpaperDirectory.exists()) {
                wallpaperDirectory.mkdirs();
            }
            if (currentFile.exists()) {
                InputStream in = new FileInputStream(currentFile);
                OutputStream out = new FileOutputStream(newfile);
                // Copy the bits from instream to outstream
                byte[] buf = new byte[1024];
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
                in.close();
                out.close();
                Log.e("vii", "Video file saved successfully.");
            } else {
                Log.e("vii", "Video saving failed. Source file missing.");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public String saveImage(Bitmap myBitmap) {
        ByteArrayOutputStream bytes = new ByteArrayOutputStream();
        myBitmap.compress(Bitmap.CompressFormat.JPEG, 80, bytes);
        File wallpaperDirectory = new File(
                Environment.getExternalStorageDirectory() + "" + getResources().getString(R.string.app_name));
        // have the object build the directory structure, if needed.
        if (!wallpaperDirectory.exists()) {
            wallpaperDirectory.mkdirs();
        }

        try {
            File f = new File(wallpaperDirectory, Calendar.getInstance()
                    .getTimeInMillis() + ".jpg");
            f.createNewFile();
            FileOutputStream fo = new FileOutputStream(f);
            fo.write(bytes.toByteArray());
            MediaScannerConnection.scanFile(UploadVideo.this,
                    new String[]{f.getPath()},
                    new String[]{"image/jpeg"}, null);
            fo.close();
            Log.e("TAG", "File Saved::--->" + f.getAbsolutePath());

            return f.getAbsolutePath();
        } catch (IOException e1) {
            e1.printStackTrace();
        }
        return "";
    }

    //We are calling this method to check the permission status
    private boolean isReadStorageAllowed() {
        if (ContextCompat.checkSelfPermission(UploadVideo.this,
                Manifest.permission.READ_EXTERNAL_STORAGE) + ContextCompat
                .checkSelfPermission(UploadVideo.this,
                        Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED)
            return true;
        return false;
    }

    //Requesting permission
    private void requestStoragePermission() {

        if (ActivityCompat.shouldShowRequestPermissionRationale
                (UploadVideo.this, Manifest.permission.READ_EXTERNAL_STORAGE) &&
                ActivityCompat.shouldShowRequestPermissionRationale
                        (UploadVideo.this, Manifest.permission.CAMERA)) {
        }
        ActivityCompat.requestPermissions(UploadVideo.this, new String[]{Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.CAMERA}, MY_CAMERA_REQUEST_CODE);
    }

    private String getRealPathFromURI(Context context, Uri contentUri) {
        Cursor cursor = null;
        try {
            String[] proj = {MediaStore.Images.Media.DATA};
            cursor = context.getContentResolver().query(contentUri, proj, null, null, null);
            int column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
            cursor.moveToFirst();
            return cursor.getString(column_index);
        } catch (Exception e) {
            Log.e("==>", "getRealPathFromURI Exception : " + e.toString());
            return "";
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    public void SetUploadquote() {


        progressDialog.show();

        Call<UploadVideoModel> call;

        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();

        RequestBody requestFile = RequestBody.create(MediaType.parse("image/*"), file_thumbnail);
        MultipartBody.Part body =
                MultipartBody.Part.createFormData("video_thumbnail", file_thumbnail.getName(), requestFile);

        RequestBody requestFile1 = RequestBody.create(MediaType.parse("video/*"), file);
        MultipartBody.Part body1 =
                MultipartBody.Part.createFormData("video_upload", file.getName(), requestFile1);

        RequestBody user_id = RequestBody.create(MediaType.parse("text/plain"), "" + prefManager.getLoginId());
        RequestBody video_title = RequestBody.create(MediaType.parse("text/plain"), "" + str_Video_Title);
        RequestBody cat_id = RequestBody.create(MediaType.parse("text/plain"), "" + str_Selectcat_id);
        RequestBody v_duration = RequestBody.create(MediaType.parse("text/plain"), "30" );
        RequestBody sound_id = RequestBody.create(MediaType.parse("text/plain"), Constant.Selected_sound_id);

        call = bookNPlayAPI.upload_video(body, body1, user_id, video_title, cat_id,v_duration,sound_id);

        call.enqueue(new Callback<UploadVideoModel>() {
            @Override
            public void onResponse(Call<UploadVideoModel> call, Response<UploadVideoModel> response) {
                progressDialog.dismiss();
                Log.e("response", "" + response.body());
                if (response.code() == 200) {
                    new AlertDialog.Builder(UploadVideo.this)
                            .setTitle(getResources().getString(R.string.app_name))
                            .setMessage("" + response.body().getMessage())
                            .setCancelable(false)
                            .setPositiveButton("ok", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                    finish();
                                }
                            }).show();
                } else {
                    new AlertDialog.Builder(UploadVideo.this)
                            .setTitle(""+getResources().getString(R.string.app_name))
                            .setMessage("" + response.body().getMessage())
                            .setCancelable(false)
                            .setPositiveButton("ok", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                }
                            }).show();
                }
            }

            @Override
            public void onFailure(Call<UploadVideoModel> call, Throwable t) {
                progressDialog.dismiss();
                Log.e("t", "Video Upload Success");
                new AlertDialog.Builder(UploadVideo.this)
                        .setTitle(getResources().getString(R.string.app_name))
                        .setMessage("" + t.getMessage())
                        .setCancelable(false)
                        .setPositiveButton("ok", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                finish();
                            }
                        }).show();
            }
        });
    }

}
