package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.makeramen.roundedimageview.RoundedImageView;
import com.squareup.picasso.Picasso;
import com.videostatus.Activity.VideoList;
import com.videostatus.Model.CategoryModel.Result;
import com.videostatus.R;

import java.util.List;

public class CategoryAdapter2 extends RecyclerView.Adapter<CategoryAdapter2.MyViewHolder> {

    private List<Result> CategoryList;
    Context mcontext;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        public TextView title, year, genre, txt_share_cnt, txt_view;
        ImageView iv_thumb, iv_copy;
        LinearLayout ly_main;

        public MyViewHolder(View view) {
            super(view);
            title = (TextView) view.findViewById(R.id.title);
            iv_thumb = (ImageView) view.findViewById(R.id.iv_thumb);
            ly_main = view.findViewById(R.id.ly_main);
        }
    }


    public CategoryAdapter2(Context context, List<Result> moviesList) {
        this.CategoryList = moviesList;
        this.mcontext = context;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.cate_item_row2, parent, false);

        return new MyViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        holder.title.setText(CategoryList.get(position).getCategoryName());

        Picasso.with(mcontext).load(CategoryList.get(position).getCategoryImage())
                .into(holder.iv_thumb);

        holder.ly_main.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Log.e("click", "call");
                Intent intent = new Intent(mcontext, VideoList.class);
                intent.putExtra("Id", CategoryList.get(position).getId());
                intent.putExtra("Name", CategoryList.get(position).getCategoryName());
                mcontext.startActivity(intent);
            }
        });
    }

    @Override
    public int getItemCount() {
        return CategoryList.size();
    }

}
