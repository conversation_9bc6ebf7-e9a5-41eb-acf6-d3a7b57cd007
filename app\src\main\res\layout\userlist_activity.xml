<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/white"
                app:popupTheme="@style/AppTheme.PopupOverlay"
                app:titleTextColor="@color/colorPrimary">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/ly_back"
                        android:layout_width="30dp"
                        android:layout_height="match_parent">

                        <TextView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_gravity="center_vertical"
                            android:background="@drawable/ic_back"
                            android:backgroundTint="@color/colorPrimary"
                            android:gravity="center"
                            android:textSize="16dp" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/toolbar_title"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:gravity="center"
                        android:textColor="@color/colorPrimary"
                        android:textSize="16dp" />
                </LinearLayout>
            </androidx.appcompat.widget.Toolbar>

        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <ir.alirezabdn.wp7progress.WP10ProgressBar
                android:id="@+id/wp7progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                app:indicatorColor="@color/colorAccent"
                app:indicatorHeight="7"
                app:indicatorRadius="5"
                app:interval="100" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_userlist"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:nestedScrollingEnabled="false"
                    android:scrollbars="none"
                    android:visibility="visible" />

            </LinearLayout>

        </RelativeLayout>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>