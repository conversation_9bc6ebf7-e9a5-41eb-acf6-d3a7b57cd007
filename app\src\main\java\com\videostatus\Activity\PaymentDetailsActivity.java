package com.videostatus.Activity;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdSize;
import com.google.android.gms.ads.AdView;
import com.squareup.picasso.Picasso;
import com.videostatus.Model.SuccessModel.SuccessModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import static com.squareup.picasso.Picasso.Priority.HIGH;

public class PaymentDetailsActivity extends AppCompatActivity {

    TextView toolbar_title;
    Toolbar toolbar;

    LinearLayout ly_back;
    PrefManager prefManager;

    RelativeLayout rl_adView;

    RadioGroup radioGroup;

    TextView txt_submit;
    EditText et_payment_details;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.payment_details);
        PrefManager.forceRTLIfSupported(getWindow(), PaymentDetailsActivity.this);
        prefManager = new PrefManager(PaymentDetailsActivity.this);

        toolbar = findViewById(R.id.toolbar);

        toolbar_title = findViewById(R.id.toolbar_title);
        toolbar_title.setText(getResources().getString(R.string.payment_details));

        rl_adView = findViewById(R.id.rl_adView);
        radioGroup = findViewById(R.id.radioGroup);
        txt_submit = findViewById(R.id.txt_submit);
        et_payment_details = findViewById(R.id.et_payment_details);

        txt_submit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RadioButton rb = radioGroup.findViewById(
                        radioGroup.getCheckedRadioButtonId());
                Toast.makeText(PaymentDetailsActivity.this, rb.getText(),
                        Toast.LENGTH_SHORT).show();

                String payment_type = rb.getText().toString();
                String payment_details = et_payment_details.getText().toString();

                if (payment_details.isEmpty()) {
                    Toast.makeText(PaymentDetailsActivity.this, "Please enter payment details",
                            Toast.LENGTH_SHORT).show();
                    return;
                }

                Withdrawal_request(payment_type, payment_details);
            }
        });

        ly_back = findViewById(R.id.ly_back);
        ly_back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        if (prefManager.getValue("banner_ad").equalsIgnoreCase("yes")) {
            Admob();
            rl_adView.setVisibility(View.VISIBLE);
        } else {
            rl_adView.setVisibility(View.GONE);
        }

    }

    public void Admob() {

        try {
            AdView mAdView = new AdView(PaymentDetailsActivity.this);
            mAdView.setAdSize(AdSize.SMART_BANNER);
            mAdView.setAdUnitId(prefManager.getValue("banner_adid"));
            AdRequest adRequest = new AdRequest.Builder().build();
            mAdView.setAdListener(new AdListener() {
                @Override
                public void onAdLoaded() {
                }

                @Override
                public void onAdClosed() {
//                    Toast.makeText(getApplicationContext(), "Ad is closed!", Toast.LENGTH_SHORT).show();
                }

                @Override
                public void onAdFailedToLoad(int errorCode) {
                    Log.e("errorcode", "" + errorCode);
//                    Toast.makeText(getApplicationContext(), "Ad failed to load! error code: " + errorCode, Toast.LENGTH_SHORT).show();
                }

                @Override
                public void onAdLeftApplication() {
//                    Toast.makeText(getApplicationContext(), "Ad left application!", Toast.LENGTH_SHORT).show();
                }

                @Override
                public void onAdOpened() {
                    super.onAdOpened();
                }
            });
            mAdView.loadAd(adRequest);

            ((RelativeLayout) rl_adView).addView(mAdView);
        } catch (Exception e) {
            Log.e("Exception=>", "" + e.getMessage());
        }
    }

    private void Withdrawal_request(String payment_type, String payment_details) {
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.withdrawal_request(prefManager.getLoginId(),
                payment_type, payment_details);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                Log.e("Withdrawal_request", "" + response.body().getMessage());
                new AlertDialog.Builder(PaymentDetailsActivity.this)
                        .setTitle("" + getResources().getString(R.string.app_name))
                        .setMessage("" + response.body().getMessage())
                        .setCancelable(false)
                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                finish();
                            }
                        }).show();
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
            }
        });
    }
}
