<component name="libraryTable">
  <library name="Gradle: com.wonderkiln:camerakit:0.13.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/1c60577bbb557841b532d474c90703d2/jetified-camerakit-0.13.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/1c60577bbb557841b532d474c90703d2/jetified-camerakit-0.13.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/1c60577bbb557841b532d474c90703d2/jetified-camerakit-0.13.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/1c60577bbb557841b532d474c90703d2/jetified-camerakit-0.13.0/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>