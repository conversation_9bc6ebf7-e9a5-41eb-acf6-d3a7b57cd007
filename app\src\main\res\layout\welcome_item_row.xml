<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/profilContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:foreground="?attr/selectableItemBackground">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="45dp"
        android:layout_marginTop="25dp"
        android:layout_marginRight="45dp"
        android:layout_marginBottom="25dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/title">

                <ImageView
                    android:id="@+id/imagePage1"
                    android:layout_width="100dp"
                    android:layout_height="96dp"
                    android:layout_centerHorizontal="true"
                    android:layout_centerVertical="true"
                    android:scaleType="centerCrop"
                    android:src="@mipmap/ic_launcher" />

            </RelativeLayout>

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_above="@+id/description"
                android:gravity="center_horizontal"
                android:text="Overview"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:gravity="center_horizontal"
                android:paddingLeft="46dp"
                android:paddingTop="8dp"
                android:paddingRight="46dp"
                android:paddingBottom="58dp"
                android:text="Making friends is easy as waving your hand back and forth."
                android:textSize="11sp" />

        </RelativeLayout>

    </androidx.cardview.widget.CardView>
</FrameLayout>