<component name="libraryTable">
  <library name="Gradle: androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/df214170bb2be54c05ab0f2f19df054f/slidingpanelayout-1.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/df214170bb2be54c05ab0f2f19df054f/slidingpanelayout-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/df214170bb2be54c05ab0f2f19df054f/slidingpanelayout-1.0.0/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.slidingpanelayout/slidingpanelayout/1.0.0/f3f2e4fded24d5969a86e1974ad7e96975d970a0/slidingpanelayout-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>