package com.videostatus.Activity;

import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SnapHelper;

import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.android.exoplayer2.ExoPlayerFactory;
import com.google.android.exoplayer2.PlaybackParameters;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.Timeline;
import com.google.android.exoplayer2.source.ExtractorMediaSource;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.TrackGroupArray;
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
import com.google.android.exoplayer2.trackselection.TrackSelectionArray;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.google.android.exoplayer2.util.Util;
import com.videostatus.Adapter.SingalVideoAdapter;
import com.videostatus.Fragment.Comment;
import com.videostatus.Model.SuccessModel.SuccessModel;
import com.videostatus.Model.VideoModel.Result;
import com.videostatus.R;
import com.videostatus.Utility.Constant;
import com.videostatus.Utility.PrefManager;
import com.videostatus.Interface.ShareAll;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;
import com.volokh.danylo.hashtaghelper.HashTagHelper;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class HomeSingalVideo extends AppCompatActivity implements ShareAll, Player.EventListener {

    public HomeSingalVideo() {
    }

    ProgressBar p_bar;
    List<Result> LatestList;
    RecyclerView recycler_latest;
    SingalVideoAdapter fullScreenAdapter;
    PrefManager prefManager;
    Context context;
    LinearLayoutManager layoutManager;
    int currentPage = 1;
    int position;
    int swipe_count = 0;
    String v_id;
    String Type = "", video_url;
    Boolean loadingWithServer = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Hide_navigation();
        setContentView(R.layout.homesingalvideo);
        context = HomeSingalVideo.this;

        MainActivity.al_title.setVisibility(View.GONE);
        MainActivity.ly_bottom.setBackground(null);
        MainActivity.ly_bottom.setBackgroundColor(getResources().getColor(R.color.transparent));

        prefManager = new PrefManager(HomeSingalVideo.this);
        p_bar = findViewById(R.id.p_bar);

        recycler_latest = findViewById(R.id.recycler_latest);
        layoutManager = new LinearLayoutManager(context);
        recycler_latest.setLayoutManager(layoutManager);
        recycler_latest.setHasFixedSize(false);

        SnapHelper snapHelper = new PagerSnapHelper();
        snapHelper.attachToRecyclerView(recycler_latest);

        // this is the scroll listener of recycler view which will tell the current item number
        recycler_latest.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);

            }

            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                //here we find the current item number
                final int scrollOffset = recyclerView.computeVerticalScrollOffset();
                final int height = recyclerView.getHeight();
                int pageno = scrollOffset / height;
                if (pageno != currentPage) {
                    currentPage = pageno;
                    Release_Privious_Player();
                    Set_Player(currentPage);
                }
            }
        });

        LatestList = new ArrayList<>();
        LatestList = Constant.VideoList;
        Log.e("LatestList", "" + LatestList);

        SetAdapter();

        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            position = bundle.getInt("position");
            Log.e("position", "" + position);
            recycler_latest.getLayoutManager().scrollToPosition(position);
        }

    }

    @Override
    public void onResume() {
        super.onResume();
        if ((privious_player != null && is_visible_to_user)) {
            privious_player.setPlayWhenReady(true);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (privious_player != null) {
            privious_player.setPlayWhenReady(false);
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        if (privious_player != null) {
            privious_player.setPlayWhenReady(false);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (privious_player != null) {
            privious_player.release();
        }
    }


    public void SetAdapter() {

        fullScreenAdapter = new SingalVideoAdapter(HomeSingalVideo.this, LatestList, "Home",
                new SingalVideoAdapter.OnItemClickListener() {
                    @Override
                    public void onItemClick(int positon, Result item, View view) {
                        v_id = item.getId();
                        video_url = item.getVideoUrl();

                        switch (view.getId()) {

                            case R.id.ly_back:
                                finish();
                                break;

                            case R.id.txt_username:
                                onPause();
                                OpenProfile(item.getUserId());
                                break;

                            case R.id.txt_whatsapp:
                                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                                    Log.e("file", "" + (CheckFile(video_url)));
                                    if (CheckFile(video_url)) {
                                        File file = new File(GetFilePath(video_url));
                                        ShareOnWhatsApp(file);
                                    } else {
                                        Type = "whatsapp";
                                        new DownloadFile().execute(video_url);
                                    }
                                } else {
                                    startActivity(new Intent(HomeSingalVideo.this, LoginActivity.class));
                                }
                                break;

                            case R.id.txt_insta:
                                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                                    Log.e("file", "" + (CheckFile(video_url)));
                                    if (CheckFile(video_url)) {
                                        File file = new File(GetFilePath(video_url));
                                        ShareOnInsta(file);
                                    } else {
                                        Type = "instagram";
                                        new DownloadFile().execute(video_url);
                                    }
                                } else {
                                    startActivity(new Intent(HomeSingalVideo.this, LoginActivity.class));
                                }
                                break;

                            case R.id.txt_facebook:
                                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                                    Log.e("file", "" + (CheckFile(video_url)));
                                    if (CheckFile(video_url)) {
                                        File file = new File(GetFilePath(video_url));
                                        ShareOnFacebook(file);
                                    } else {
                                        Type = "facebook";
                                        new DownloadFile().execute(video_url);
                                    }
                                } else {
                                    startActivity(new Intent(HomeSingalVideo.this, LoginActivity.class));
                                }
                                break;

                            case R.id.ly_shared_layout:
                                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                                    Log.e("file", "" + (CheckFile(video_url)));
                                    if (CheckFile(video_url)) {
                                        File file = new File(GetFilePath(video_url));
                                        ShareOnShare(file);
                                    } else {
                                        Type = "share";
                                        new DownloadFile().execute(video_url);
                                    }
                                } else {
                                    startActivity(new Intent(HomeSingalVideo.this, LoginActivity.class));
                                }
                                break;

                            case R.id.ly_like_layout:
                                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                                    Like_Video(positon, item);
                                } else {
                                    startActivity(new Intent(HomeSingalVideo.this, LoginActivity.class));
                                }
                                break;

                            case R.id.ly_comment_layout:
                                OpenComment(item);
                                break;

                            case R.id.iv_user_pic:
                                OpenProfile(item.getUserId());
                                break;

                            case R.id.iv_add_follow:
                                if (!prefManager.getLoginId().equalsIgnoreCase("0"))
                                    add_follow(positon, item.getUserId());
                                else
                                    startActivity(new Intent(HomeSingalVideo.this, LoginActivity.class));
                                break;

                            case R.id.txt_like:
                                ViewAPI(item.getId());
                                break;
                        }
                    }
                });

        fullScreenAdapter.setHasStableIds(true);
        recycler_latest.setAdapter(fullScreenAdapter);

    }

    @Override
    public void WhatsappShare() {

    }

    // when we swipe for another video this will relaese the privious player
    SimpleExoPlayer privious_player;

    public void Release_Privious_Player() {
        if (privious_player != null) {
            privious_player.removeListener(this);
            privious_player.release();
        }
    }

    // this will call when go to the home tab From other tab.
    // this is very importent when for video play and pause when the focus is changes
    boolean is_visible_to_user = true;
/*    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        is_visible_to_user = isVisibleToUser;

        if (privious_player != null && isVisibleToUser) {
            privious_player.setPlayWhenReady(true);
        } else if (privious_player != null && !isVisibleToUser) {
            privious_player.setPlayWhenReady(false);
        }
    }*/

    // this will call when swipe for another video and
    // this function will set the player to the current video
    public void Set_Player(final int currentPage) {

        try {

            Log.e("currentPage==>", "" + currentPage);

            DefaultTrackSelector trackSelector = new DefaultTrackSelector();
            final SimpleExoPlayer player = ExoPlayerFactory.newSimpleInstance(context, trackSelector);

            DataSource.Factory dataSourceFactory = new DefaultDataSourceFactory(context,
                    Util.getUserAgent(context, "VideoStatus"));

            MediaSource videoSource = new ExtractorMediaSource.Factory(dataSourceFactory)
                    .createMediaSource(Uri.parse(LatestList.get(currentPage).getVideoUrl()));

            Log.e("Video URL", LatestList.get(currentPage).getVideoUrl());

            player.prepare(videoSource);

            player.setRepeatMode(Player.REPEAT_MODE_ALL);
            player.addListener(this);

            View layout = layoutManager.findViewByPosition(currentPage);
            final PlayerView playerView = layout.findViewById(R.id.playerview);
            playerView.setPlayer(player);

            player.setPlayWhenReady(is_visible_to_user);
            privious_player = player;

            final RelativeLayout mainlayout = layout.findViewById(R.id.mainlayout);
            playerView.setOnTouchListener(new View.OnTouchListener() {
                private GestureDetector gestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {

                    @Override
                    public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
                        super.onFling(e1, e2, velocityX, velocityY);
                        float deltaX = e1.getX() - e2.getX();
                        float deltaXAbs = Math.abs(deltaX);
                        // Only when swipe distance between minimal and maximal distance value then we treat it as effective swipe
                        if ((deltaXAbs > 100) && (deltaXAbs < 1000)) {
                            if (deltaX > 0) {
//                            OpenProfile(LatestList.get(currentPage).getUserId(), true);
                            }
                        }

                        return true;
                    }

                    @Override
                    public boolean onSingleTapUp(MotionEvent e) {
                        super.onSingleTapUp(e);
                        if (!player.getPlayWhenReady()) {
                            privious_player.setPlayWhenReady(true);
                        } else {
                            privious_player.setPlayWhenReady(false);
                        }

                        return true;
                    }

                    @Override
                    public void onLongPress(MotionEvent e) {
                        super.onLongPress(e);
//                    Show_video_option(item);
                    }

                    @Override
                    public boolean onDoubleTap(MotionEvent e) {

                        if (!player.getPlayWhenReady()) {
                            privious_player.setPlayWhenReady(true);
                        }

//                    if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
//                        Show_heart_on_DoubleTap(item, mainlayout, e);
//                        Like_Video(currentPage, item);
//                    } else {
//                        Toast.makeText(context, "Please Login into app", Toast.LENGTH_SHORT).show();
//                    }
                        return super.onDoubleTap(e);

                    }
                });

                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    gestureDetector.onTouchEvent(event);
                    return true;
                }
            });

            TextView desc_txt = layout.findViewById(R.id.txt_desc);
            HashTagHelper.Creator.create(context.getResources().getColor(R.color.colorPrimary),
                    new HashTagHelper.OnHashTagClickListener() {
                        @Override
                        public void onHashTagClicked(String hashTag) {
                            onPause();
                            OpenHashtag(hashTag);
                        }
                    }).handle(desc_txt);

            LinearLayout soundimage = (LinearLayout) layout.findViewById(R.id.ly_sound_image_layout);
            Animation sound_animation = AnimationUtils.loadAnimation(context, R.anim.d_clockwise_rotation);
            soundimage.startAnimation(sound_animation);

            if (prefManager.getValue("interstital_ad").equalsIgnoreCase("yes")) {
                swipe_count++;
                if (swipe_count > Integer.parseInt(prefManager.getValue("interstital_adclick"))) {
                    Show_add();
                    swipe_count = 0;
                }
            }

        } catch (Exception e) {
            Log.e("Exception", "" + e.getMessage());
        }

    }

    public void Show_add() {
//        if (mInterstitialAd.isLoaded()) {
//            mInterstitialAd.show();
//        }
    }

    // this will open the profile of user which have uploaded the currenlty running video
    private void OpenHashtag(String tag) {
//
//        Taged_Videos_F taged_videos_f = new Taged_Videos_F();
//        FragmentTransaction transaction = HomeSingalVideo.this.getSupportFragmentManager().beginTransaction();
//        transaction.setCustomAnimations(R.anim.in_from_bottom, R.anim.out_to_top, R.anim.in_from_top, R.anim.out_from_bottom);
//        Bundle args = new Bundle();
//        args.putString("tag", tag);
//        taged_videos_f.setArguments(args);
//        transaction.addToBackStack(null);
//        transaction.replace(R.id.MainMenuFragment, taged_videos_f).commit();


    }

    // Bottom all the function and the Call back listener of the Expo player
    @Override
    public void onTimelineChanged(Timeline timeline, @Nullable Object manifest, int reason) {

    }

    @Override
    public void onTracksChanged(TrackGroupArray trackGroups, TrackSelectionArray trackSelections) {

    }

    @Override
    public void onLoadingChanged(boolean isLoading) {

    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {

        if (playbackState == Player.STATE_BUFFERING) {
            p_bar.setVisibility(View.VISIBLE);
        } else if (playbackState == Player.STATE_READY) {
            p_bar.setVisibility(View.GONE);
        }

    }

    @Override
    public void onRepeatModeChanged(int repeatMode) {

    }

    @Override
    public void onShuffleModeEnabledChanged(boolean shuffleModeEnabled) {

    }

    @Override
    public void onPlayerError(ExoPlaybackException error) {

    }

    @Override
    public void onPositionDiscontinuity(int reason) {

    }

    @Override
    public void onPlaybackParametersChanged(PlaybackParameters playbackParameters) {

    }

    @Override
    public void onSeekProcessed() {

    }

    // this will open the profile of user which have uploaded the currenlty running video
    private void OpenProfile(String id) {
        Intent intent = new Intent(HomeSingalVideo.this, Profile.class);
        intent.putExtra("Id", "" + prefManager.getLoginId());
        intent.putExtra("TO_Id", "" + id);
        startActivity(intent);
    }

    // this will open the comment screen
    private void OpenComment(Result item) {

        Bundle args = new Bundle();
        args.putString("video_id", item.getId());
        args.putString("user_id", prefManager.getLoginId());
        Comment comment_f = Comment.newInstance();
        comment_f.setArguments(args);
        comment_f.show(getSupportFragmentManager(), "Comment.TAG");
    }

    // this function will call for like the video and Call an Api for like the video
    public void Like_Video(final int position, final Result home_get_set) {
        String status;
        View layout = layoutManager.findViewByPosition(currentPage);
        TextView txt_like = layout.findViewById(R.id.txt_like);
        ImageView iv_like_image = layout.findViewById(R.id.iv_like_image);

        if (LatestList.get(position).getIsLike().equalsIgnoreCase("0")) {
            LatestList.get(position).setIsLike("1");
            LatestList.get(position).setTotalLike("" + (Integer.parseInt(LatestList.get(position).getTotalLike()) + 1));
            status = "1";
        } else {
            LatestList.get(position).setIsLike("0");
            status = "2";
            LatestList.get(position).setTotalLike("" + (Integer.parseInt(LatestList.get(position).getTotalLike()) - 1));
        }

        txt_like.setText("" + LatestList.get(position).getTotalLike());

        if (LatestList.get(position).getIsLike().equalsIgnoreCase("0")) {
            iv_like_image.setBackground(HomeSingalVideo.this.getResources().getDrawable(R.drawable.ic_like));
        } else {
            iv_like_image.setBackground(HomeSingalVideo.this.getResources().getDrawable(R.drawable.ic_like_fill));
        }

//        homeAdapter.notifyDataSetChanged();

        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.like_dislike(home_get_set.getId(),
                prefManager.getLoginId(), status);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                Log.e("Like DisLike", "" + response.body().getMessage());
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
            }
        });

    }

    private void add_follow(final int position, String userid) {

        View layout = layoutManager.findViewByPosition(currentPage);
        ImageView iv_add_follow = layout.findViewById(R.id.iv_add_follow);

        if (LatestList.get(position).getIs_follow().equalsIgnoreCase("0")) {
            LatestList.get(position).setIs_follow("1");
        } else {
            LatestList.get(position).setIs_follow("0");
        }

        if (LatestList.get(position).getIs_follow().equalsIgnoreCase("0")) {
            iv_add_follow.setBackground(HomeSingalVideo.this.getResources().getDrawable(R.drawable.ic_add_follow));
        } else {
            iv_add_follow.setBackground(HomeSingalVideo.this.getResources().getDrawable(R.drawable.ic_follow));
        }

        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.follow(prefManager.getLoginId(), userid);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                if (response.code() == 200) {
                }
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
            }
        });
    }

    public boolean CheckFile(String url1) {
        try {

            String fileName = url1.substring(url1.lastIndexOf('/') + 1,
                    url1.length());
            Log.e("fileName", "" + fileName);
            String folder = Environment.getExternalStorageDirectory() + File.separator + getResources().getString(R.string.app_name) + "/";
            //Create androiddeft folder if it does not exist
            File directory = new File(folder, fileName);

            Log.e("directory", "" + directory);

            if (directory.exists()) {
                return true;
            } else {
                return false;
            }

        } catch (Exception e) {
            Log.e("Exception", "" + e.getMessage());
        }
        return false;
    }

    private class DownloadFile extends AsyncTask<String, String, String> {

        private ProgressDialog progressDialog;
        private String fileName;
        private String folder;
        private boolean isDownloaded;

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            this.progressDialog = new ProgressDialog(HomeSingalVideo.this);
            this.progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
            this.progressDialog.setCancelable(false);
            this.progressDialog.show();
        }

        @Override
        protected String doInBackground(String... f_url) {
            int count;
            try {
                URL url = new URL(f_url[0]);
                URLConnection connection = url.openConnection();
                connection.connect();
                // getting file length
                int lengthOfFile = connection.getContentLength();
                // input stream to read file - with 8k buffer
                InputStream input = new BufferedInputStream(url.openStream(), 8192);
                String timestamp = new SimpleDateFormat("yy_MM_dd_HH_mm_ss").format(new Date());
                //Extract file name from URL
                fileName = f_url[0].substring(f_url[0].lastIndexOf('/') + 1, f_url[0].length());
                //Append timestamp to file name
//                fileName = timestamp + "_" + fileName;
                //External directory path to save file
                Log.e("fileName", fileName);
                folder = Environment.getExternalStorageDirectory() + File.separator + getResources().getString(R.string.app_name) + "/";
                //Create androiddeft folder if it does not exist
                File directory = new File(folder);

                if (!directory.exists()) {
                    directory.mkdirs();
                }
                // Output stream to write file
                OutputStream output = new FileOutputStream(folder + fileName);
                byte data[] = new byte[1024];
                long total = 0;
                while ((count = input.read(data)) != -1) {
                    total += count;
                    // publishing the progress....
                    // After this onProgressUpdate will be called
                    publishProgress("" + (int) ((total * 100) / lengthOfFile));
                    Log.e("==>Pro==>", "Progress: " + (int) ((total * 100) / lengthOfFile));
                    // writing data to file
                    output.write(data, 0, count);
                }
                // flushing output
                output.flush();
                // closing streams
                output.close();
                input.close();
                return "" + folder + fileName;

            } catch (Exception e) {
                Log.e("Error: ", e.getMessage());
            }
            return "Something went wrong";
        }

        protected void onProgressUpdate(String... progress) {
            // setting progress percentage
            progressDialog.setProgress(Integer.parseInt(progress[0]));
        }

        @Override
        protected void onPostExecute(String message) {
            // dismiss the dialog after the file was downloaded
            this.progressDialog.dismiss();
            // Display File path after downloading
            Log.e("file-path", "" + message);
//            Toast.makeText(getApplicationContext(),
//                    message, Toast.LENGTH_LONG).show();
            File file = new File(message);
            Log.e("file", "" + file);

            if (Type.equalsIgnoreCase("whatsapp"))
                ShareOnWhatsApp(file);
            else if (Type.equalsIgnoreCase("facebook"))
                ShareOnFacebook(file);
            else if (Type.equalsIgnoreCase("facebook_messanger"))
                ShareOnFacebookMessanger(file);
            else if (Type.equalsIgnoreCase("instagram"))
                ShareOnInsta(file);
            else if (Type.equalsIgnoreCase("twitter"))
                ShareOnTwitter(file);
            else if (Type.equalsIgnoreCase("snapchat"))
                ShareOnSnap(file);
            else if (Type.equalsIgnoreCase("share"))
                ShareOnShare(file);
            else if (Type.equalsIgnoreCase("download")) {
                DownloadAPI();
                Toast.makeText(HomeSingalVideo.this, "Download at:-" + message, Toast.LENGTH_SHORT).show();
            } else if (Type.equalsIgnoreCase("facebook_messanger"))
                ShareOnFacebookMessanger(file);
        }
    }

    public String GetFilePath(String url1) {
        try {

            String fileName = url1.substring(url1.lastIndexOf('/') + 1,
                    url1.length());
            Log.e("fileName", "" + fileName);
            String folder = Environment.getExternalStorageDirectory() + File.separator + getResources().getString(R.string.app_name) + "/";
            //Create androiddeft folder if it does not exist
            File directory = new File(folder, fileName);

            Log.e("directory", "" + directory);

            return "" + directory;

        } catch (Exception e) {
            Log.e("Exception", "" + e.getMessage());
        }
        return "";
    }

    public void ShareOnWhatsApp(File file) {
        try {
            Uri videoUri = FileProvider.getUriForFile(
                    HomeSingalVideo.this, getApplicationContext().getPackageName() + ".provider", file);
            Log.e("uri", "" + videoUri);
            Intent videoshare = new Intent(Intent.ACTION_SEND);
            videoshare.setType("*/*");
            videoshare.setPackage("com.whatsapp");
            videoshare.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            videoshare.putExtra(Intent.EXTRA_STREAM, videoUri);
            startActivity(videoshare);
        } catch (Exception e) {
            Log.e("Exception", "" + e.getMessage());
            Toast.makeText(HomeSingalVideo.this, "WhatsApp not available", Toast.LENGTH_SHORT).show();
        }
    }

    public void ShareOnFacebook(File file) {
        try {
            Uri videoUri = FileProvider.getUriForFile(
                    HomeSingalVideo.this, getApplicationContext().getPackageName() + ".provider", file);
            Log.e("uri", "" + videoUri);

            Uri imageUri = FileProvider.getUriForFile(HomeSingalVideo.this,
                    HomeSingalVideo.this.getApplicationContext().getPackageName() + ".provider", file);
            Intent shareIntent = new Intent();
            shareIntent.setAction(Intent.ACTION_SEND);
            shareIntent.setPackage("com.facebook.katana");
            shareIntent.putExtra(Intent.EXTRA_STREAM, imageUri);
            shareIntent.setType("video/mp4");
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            startActivity(shareIntent);
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("ActivityNotF", "" + ex.getMessage());
            alertForApp(getString(R.string.install_facebook), "com.facebook.katana");
        }
    }

    public void ShareOnFacebookMessanger(File file) {
        try {
            Uri videoUri = FileProvider.getUriForFile(
                    HomeSingalVideo.this, getApplicationContext().getPackageName() + ".provider", file);
            Log.e("uri", "" + videoUri);
            Intent shareIntent = new Intent();
            shareIntent.setAction(Intent.ACTION_SEND);
            shareIntent.setPackage("com.facebook.orca");
            shareIntent.putExtra(Intent.EXTRA_STREAM, videoUri);
            shareIntent.setType("video/mp4");
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            startActivity(shareIntent);
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("Activity", "" + ex.getMessage());
            alertForApp(getString(R.string.install_messenger), "com.facebook.orca");
        }
    }

    public void ShareOnInsta(File file) {

        try {
            Uri videoUri = FileProvider.getUriForFile(
                    HomeSingalVideo.this, getApplicationContext().getPackageName() + ".provider", file);
            Log.e("uri", "" + videoUri);
            Intent shareIntent = new Intent();
            shareIntent.setAction(Intent.ACTION_SEND);
            shareIntent.setPackage("com.instagram.android");
            shareIntent.putExtra(Intent.EXTRA_STREAM, videoUri);
            shareIntent.setType("video/mp4");
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            startActivity(shareIntent);
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("ActivityNotF", "" + ex.getMessage());
            alertForApp(getString(R.string.install_insta), "com.instagram.android");
        }

    }

    public void ShareOnTwitter(File file) {
        Uri videoUri = FileProvider.getUriForFile(
                HomeSingalVideo.this, getApplicationContext().getPackageName() + ".provider", file);
        Log.e("uri", "" + videoUri);
        Intent shareIntent = new Intent();
        shareIntent.setAction(Intent.ACTION_SEND);
        shareIntent.setPackage("com.twitter.android");
        shareIntent.putExtra(Intent.EXTRA_STREAM, videoUri);
        shareIntent.setType("video/mp4");
        shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        try {
            startActivity(shareIntent);
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("ActivityNotF", "" + ex.getMessage());
            alertForApp(getString(R.string.install_twitter), "com.twitter.android");
        }
    }

    public void ShareOnSnap(File file) {
        Uri videoUri = FileProvider.getUriForFile(
                HomeSingalVideo.this, getApplicationContext().getPackageName() + ".provider", file);
        Log.e("uri", "" + videoUri);
        Intent shareIntent = new Intent();
        shareIntent.setAction(Intent.ACTION_SEND);
        shareIntent.setPackage("com.snapchat.android");
        shareIntent.putExtra(Intent.EXTRA_STREAM, videoUri);
        shareIntent.setType("video/mp4");
        shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        try {
            startActivity(shareIntent);
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("ActivityNotF", "" + ex.getMessage());
            alertForApp(getString(R.string.install_snap), "com.snapchat.android");
        }
    }

    public void ShareOnShare(File file) {
        ShareAPI();
        Uri videoUri = FileProvider.getUriForFile(
                HomeSingalVideo.this, getApplicationContext().getPackageName() + ".provider", file);
        Log.e("uri", "" + videoUri);
        Intent shareIntent = new Intent(
                Intent.ACTION_SEND);
        shareIntent.setType("video/*");
        shareIntent.putExtra(Intent.EXTRA_STREAM, videoUri);
        shareIntent
                .addFlags(Intent.FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET);
        try {
            startActivity(Intent.createChooser(shareIntent, "Share with"));
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("ActivityNotF", "" + ex.getMessage());
        }
    }

    public void DownloadAPI() {
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.add_dowanload(v_id, prefManager.getLoginId());
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                if (response.code() == 200) {
                }
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
            }
        });
    }

    public void ShareAPI() {
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.share_video(v_id);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                if (response.code() == 200) {
                }
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
            }
        });
    }

    private void alertForApp(String errorMessage, final String packageToRedirect) {
        AlertDialog.Builder builder = new AlertDialog.Builder(HomeSingalVideo.this);
        builder.setTitle(R.string.app_not_found);
        builder.setIcon(R.drawable.ic_error);
        builder.setMessage(errorMessage);
        builder.setCancelable(false);
        builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int arg1) {
                String url = getString(R.string.playStore_address) + packageToRedirect;
                Intent i = new Intent(Intent.ACTION_VIEW);
                i.setData(Uri.parse(url));
                startActivity(i);
                dialog.cancel();
            }
        }).setNegativeButton("CANCEl", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int arg1) {
                dialog.cancel();
            }
        });
        AlertDialog alertDialog = builder.create();
        alertDialog.show();

    }

    // this will hide the bottom mobile navigation controll
    public void Hide_navigation() {

        requestWindowFeature(Window.FEATURE_NO_TITLE);
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        final int flags = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;

        // This work only for android 4.4+
        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {

            getWindow().getDecorView().setSystemUiVisibility(flags);

            // Code below is to handle presses of Volume up or Volume down.
            // Without this, after pressing volume buttons, the navigation bar will
            // show up and won't hide
            final View decorView = getWindow().getDecorView();
            decorView
                    .setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
                        @Override
                        public void onSystemUiVisibilityChange(int visibility) {
                            if ((visibility & View.SYSTEM_UI_FLAG_FULLSCREEN) == 0) {
                                decorView.setSystemUiVisibility(flags);
                            }
                        }
                    });
        }

    }

    public void ViewAPI(String id) {
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.video_view(id);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                if (response.code() == 200) {
                }
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
            }
        });
    }

}
