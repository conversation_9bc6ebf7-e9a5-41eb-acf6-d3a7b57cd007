<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ly_main"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible"
        tools:ignore="MissingConstraints">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:layout_margin="5dp"
            android:orientation="vertical"
            app:cardCornerRadius="15dp"
            tools:ignore="MissingConstraints">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:weightSum="1.0">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.25"
                    android:gravity="center">

                    <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/iv_thumb"
                        android:layout_width="45dp"
                        android:layout_height="45dp"
                        android:background="@drawable/ic_money"
                        android:elevation="1dp"
                        app:riv_border_color="@color/white"
                        app:riv_border_width="0dip"
                        app:riv_oval="true"
                        app:riv_tile_mode="clamp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.45"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/txt_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:text="Current Points"
                        android:textColor="@color/font_black"
                        android:textSize="@dimen/text_14" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_marginTop="5dp"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:layout_marginStart="5dp"
                            android:textColor="@color/font_black"
                            android:background="@drawable/ic_clock"
                            android:textSize="@dimen/text_12" />

                        <TextView
                            android:id="@+id/txt_date"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center"
                            android:text="03-03-2020 13:00 PM"
                            android:textColor="@color/font_dark"
                            android:textSize="@dimen/text_12" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/txt_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:layout_marginTop="5dp"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:visibility="gone"
                        android:textColor="@color/font_dark"
                        android:textSize="@dimen/text_12" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.3"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/txt_points"
                        android:layout_width="match_parent"
                        android:layout_height="30dp"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginBottom="3dp"
                        android:background="@drawable/round_bg_yellow"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:singleLine="true"
                        android:textColor="@color/font_black"
                        android:textSize="@dimen/text_12" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>