<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="110dp">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="8dp"
        app:cardCornerRadius="10dp"
        tools:ignore="MissingConstraints">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:weightSum="1.0"
            tools:ignore="MissingConstraints">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.3"
                android:gravity="center">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ImageView
                        android:id="@+id/iv_sound"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:scaleType="fitXY" />

                    <TextView
                        android:id="@+id/txt_play"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/ic_play"
                        android:scaleType="fitCenter"
                        android:visibility="visible" />

                    <TextView
                        android:id="@+id/txt_pause"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/ic_pause"
                        android:scaleType="fitCenter"
                        android:visibility="gone" />


                    <com.github.ybq.android.spinkit.SpinKitView
                        android:id="@+id/sp_loading"
                        style="@style/SpinKitView.Large.Wave"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerInParent="true"
                        android:visibility="gone"
                        app:SpinKit_Color="@color/white"
                        app:layout_constraintBottom_toBottomOf="@+id/txt_play"
                        app:layout_constraintEnd_toEndOf="@+id/iv_sound"
                        app:layout_constraintStart_toStartOf="@+id/iv_sound"
                        app:layout_constraintTop_toTopOf="@+id/txt_play" />

                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txt_sound_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:text="Name of the sounds"
                    android:textColor="@color/font_dark"
                    android:textSize="14dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.2"
                android:gravity="center">

                <ImageView
                    android:id="@+id/iv_done"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/ic_correct"
                    android:scaleType="fitCenter" />
            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>

