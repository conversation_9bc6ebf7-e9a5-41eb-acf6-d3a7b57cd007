<component name="libraryTable">
  <library name="Gradle: androidx.exifinterface:exifinterface:1.0.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/c76eb067af85d4078e49b0a3124ada7c/exifinterface-1.0.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/c76eb067af85d4078e49b0a3124ada7c/exifinterface-1.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/c76eb067af85d4078e49b0a3124ada7c/exifinterface-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/c76eb067af85d4078e49b0a3124ada7c/exifinterface-1.0.0/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.exifinterface/exifinterface/1.0.0/cb592500decea684137bd17587a92eee4e2568e/exifinterface-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>