<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-core:2.10.3@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/a868f5641fc3a78cf1d256fe9d132bcc/jetified-exoplayer-core-2.10.3/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/a868f5641fc3a78cf1d256fe9d132bcc/jetified-exoplayer-core-2.10.3/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/a868f5641fc3a78cf1d256fe9d132bcc/jetified-exoplayer-core-2.10.3/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/a868f5641fc3a78cf1d256fe9d132bcc/jetified-exoplayer-core-2.10.3/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-core/2.10.3/98258a5356393ed0868973eb2e4025b444e9e022/exoplayer-core-2.10.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-core/2.10.3/74ad56c70b111391a20dd0a1ea0f3e0f301d3a49/exoplayer-core-2.10.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>