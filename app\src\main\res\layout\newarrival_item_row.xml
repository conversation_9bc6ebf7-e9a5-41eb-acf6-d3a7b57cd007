<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:id="@+id/cardview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp"
            android:visibility="visible"
            app:cardCornerRadius="8dp"
            app:cardElevation="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="120dp">

                    <ImageView
                        android:id="@+id/iv_thumb"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="fitXY"
                        android:src="@drawable/no_image" />

                    <TextView
                        android:id="@+id/txt_bookmark"
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/ic_play"
                        android:gravity="center" />

                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:weightSum="1.0">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="@dimen/margin_5"
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:weightSum="1">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:textSize="12dp"
                            android:textStyle="bold"
                            android:weightSum="1.0">

                            <TextView
                                android:id="@+id/txt_title"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="0.6"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center_vertical"
                                android:singleLine="true"
                                android:textColor="@color/font_dark"
                                android:textSize="12dp" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.4"
                                android:gravity="center_vertical|right">

                                <TextView
                                    android:layout_width="12dp"
                                    android:layout_height="12dp"
                                    android:layout_marginStart="5dp"
                                    android:background="@drawable/ic_eye"
                                    android:gravity="center_vertical" />

                                <TextView
                                    android:id="@+id/txt_view"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="3dp"
                                    android:layout_marginRight="5dp"
                                    android:fontFamily="@font/public_medium"
                                    android:gravity="center_vertical"
                                    android:textColor="@color/grey_60"
                                    android:textSize="12dp" />
                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:textSize="12dp"
                            android:textStyle="bold"
                            android:weightSum="1.0">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center_vertical"
                                android:visibility="visible">

                                <com.iarcuschin.simpleratingbar.SimpleRatingBar
                                    android:id="@+id/ratingbar"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:srb_borderColor="@color/rating_fill"
                                    app:srb_fillColor="@color/rating_fill"
                                    app:srb_numberOfStars="5"
                                    app:srb_rating="3"
                                    app:srb_starSize="12dp" />

                                <TextView
                                    android:id="@+id/txt_avg"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="3dp"
                                    android:layout_marginEnd="3dp"
                                    android:fontFamily="@font/public_medium"
                                    android:gravity="center"
                                    android:singleLine="true"
                                    android:textColor="@color/font_dark"
                                    android:textIsSelectable="true"
                                    android:textSize="@dimen/text_12" />

                            </LinearLayout>


                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>