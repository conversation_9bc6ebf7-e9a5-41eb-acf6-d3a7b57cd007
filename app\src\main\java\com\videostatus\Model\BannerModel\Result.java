
package com.videostatus.Model.BannerModel;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class Result {

    @SerializedName("id")
    @Expose
    private String id;
    @SerializedName("cat_id")
    @Expose
    private String catId;
    @SerializedName("user_id")
    @Expose
    private String userId;
    @SerializedName("video_title")
    @Expose
    private String videoTitle;
    @SerializedName("video_duration")
    @Expose
    private String videoDuration;
    @SerializedName("video_url")
    @Expose
    private String videoUrl;
    @SerializedName("thumbnail_img")
    @Expose
    private String thumbnailImg;
    @SerializedName("type")
    @Expose
    private String type;
    @SerializedName("image")
    @Expose
    private String image;
    @SerializedName("gif")
    @Expose
    private String gif;
    @SerializedName("video_descripation")
    @Expose
    private String videoDescripation;
    @SerializedName("view")
    @Expose
    private String view;
    @SerializedName("dowanload")
    @Expose
    private String dowanload;
    @SerializedName("status")
    @Expose
    private String status;
    @SerializedName("c_date")
    @Expose
    private String cDate;
    @SerializedName("m_date")
    @Expose
    private String mDate;
    @SerializedName("category_name")
    @Expose
    private String categoryName;
    @SerializedName("totle_like")
    @Expose
    private String totleLike;
    @SerializedName("avg_rating")
    @Expose
    private String avgRating;
    @SerializedName("total_comment")
    @Expose
    private String totalComment;
    @SerializedName("total_dislike")
    @Expose
    private String totalDislike;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCatId() {
        return catId;
    }

    public void setCatId(String catId) {
        this.catId = catId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getVideoTitle() {
        return videoTitle;
    }

    public void setVideoTitle(String videoTitle) {
        this.videoTitle = videoTitle;
    }

    public String getVideoDuration() {
        return videoDuration;
    }

    public void setVideoDuration(String videoDuration) {
        this.videoDuration = videoDuration;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getThumbnailImg() {
        return thumbnailImg;
    }

    public void setThumbnailImg(String thumbnailImg) {
        this.thumbnailImg = thumbnailImg;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getGif() {
        return gif;
    }

    public void setGif(String gif) {
        this.gif = gif;
    }

    public String getVideoDescripation() {
        return videoDescripation;
    }

    public void setVideoDescripation(String videoDescripation) {
        this.videoDescripation = videoDescripation;
    }

    public String getView() {
        return view;
    }

    public void setView(String view) {
        this.view = view;
    }

    public String getDowanload() {
        return dowanload;
    }

    public void setDowanload(String dowanload) {
        this.dowanload = dowanload;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCDate() {
        return cDate;
    }

    public void setCDate(String cDate) {
        this.cDate = cDate;
    }

    public String getMDate() {
        return mDate;
    }

    public void setMDate(String mDate) {
        this.mDate = mDate;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getTotleLike() {
        return totleLike;
    }

    public void setTotleLike(String totleLike) {
        this.totleLike = totleLike;
    }

    public String getAvgRating() {
        return avgRating;
    }

    public void setAvgRating(String avgRating) {
        this.avgRating = avgRating;
    }

    public String getTotalComment() {
        return totalComment;
    }

    public void setTotalComment(String totalComment) {
        this.totalComment = totalComment;
    }

    public String getTotalDislike() {
        return totalDislike;
    }

    public void setTotalDislike(String totalDislike) {
        this.totalDislike = totalDislike;
    }

}
