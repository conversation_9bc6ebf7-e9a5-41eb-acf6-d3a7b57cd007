<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="visible"
        android:padding="3dp"
        tools:ignore="MissingConstraints">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/iv_thumb"
            android:layout_width="75dp"
            android:layout_height="75dp"
            android:scaleType="fitXY"
            app:riv_border_width="4dip"
            app:riv_mutate_background="true"
            app:riv_oval="true"
            app:riv_border_color="@color/white"
            android:layout_margin="5dp"
            android:elevation="5dp"
            app:riv_tile_mode="clamp" />

        <LinearLayout
            android:layout_width="75dp"
            android:layout_height="wrap_content"
            android:weightSum="1.0">

            <TextView
                android:id="@+id/txt_bookname"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/public_regular"
                android:gravity="center"
                android:maxLines="2"
                android:textColor="@color/font_dark"
                android:textIsSelectable="true"
                android:textSize="@dimen/text_12" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>