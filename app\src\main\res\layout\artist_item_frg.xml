<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible"
        tools:ignore="MissingConstraints">

        <androidx.cardview.widget.CardView
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:orientation="vertical"
            app:cardCornerRadius="20dp"
            android:layout_margin="10dp"
            tools:ignore="MissingConstraints">

            <ImageView
                android:id="@+id/iv_thumb"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY" />

        </androidx.cardview.widget.CardView>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:weightSum="1.0">

            <TextView
                android:id="@+id/txt_bookname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/public_regular"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textIsSelectable="true"
                android:textSize="@dimen/text_12" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>