<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <layer-list>
            <item android:right="5dp" android:top="5dp">
                <shape>
                    <corners android:radius="3dp" />
                    <solid android:color="#D6D6D6" />
                </shape>
            </item>
            <item android:bottom="2dp" android:left="2dp">
                <shape>
                    <gradient android:angle="270" android:endColor="#E2E2E2" android:startColor="#BABABA" />
                    <stroke android:width="1dp" android:color="#BABABA" />
                    <corners android:radius="4dp" />
                    <padding android:bottom="10dp" android:left="10dp" android:right="10dp" android:top="10dp" />
                </shape>
            </item>
        </layer-list>
    </item>

</selector>