package com.videostatus.Fragment;

import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.fragment.app.Fragment;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.videostatus.Adapter.CategoryAdapter2;
import com.videostatus.Model.CategoryModel.CategoryModel;
import com.videostatus.Model.CategoryModel.Result;
import com.videostatus.R;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;
import java.util.ArrayList;
import java.util.List;
import ir.alirezabdn.wp7progress.WP10ProgressBar;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CategoryList extends Fragment {

    public CategoryList() {
    }

    WP10ProgressBar progressBar;
    RecyclerView recycler_category;
    CategoryAdapter2 categoryAdapter;
    private SwipeRefreshLayout swipeContainer;
    List<Result> CategoryList;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View root = inflater.inflate(R.layout.category, container, false);

        getActivity().setTitle("Categories");

        progressBar = root.findViewById(R.id.wp7progressBar);

        recycler_category = (RecyclerView) root.findViewById(R.id.recycler_category);

        swipeContainer = (SwipeRefreshLayout) root.findViewById(R.id.swipeContainer);
        swipeContainer.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                swipeContainer.setRefreshing(true);
                getCategory();
            }
        });
        swipeContainer.setColorSchemeResources(android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light);

        getCategory();

        return root;
    }


    private void getCategory() {

        progressBar.showProgressBar();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<CategoryModel> call = bookNPlayAPI.Category();
        call.enqueue(new Callback<CategoryModel>() {
            @Override
            public void onResponse(Call<CategoryModel> call, Response<CategoryModel> response) {
                progressBar.hideProgressBar();
                swipeContainer.setRefreshing(false);
                if (response.code() == 200) {
                    CategoryList = new ArrayList<>();
                    CategoryList = response.body().getResult();
                    Log.e("CategoryList", "" + CategoryList.size());

                    categoryAdapter = new CategoryAdapter2(getActivity(), CategoryList);
//                    recycler_category.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.HORIZONTAL, true));
                    GridLayoutManager manager = new GridLayoutManager(getActivity(), 2,
                            GridLayoutManager.VERTICAL, false);
                    recycler_category.setLayoutManager(manager);
                    recycler_category.setItemAnimator(new DefaultItemAnimator());
                    recycler_category.setAdapter(categoryAdapter);
                    categoryAdapter.notifyDataSetChanged();

                }
            }

            @Override
            public void onFailure(Call<CategoryModel> call, Throwable t) {
                progressBar.hideProgressBar();
            }
        });
    }
}
