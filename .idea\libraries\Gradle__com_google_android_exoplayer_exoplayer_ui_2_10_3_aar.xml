<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-ui:2.10.3@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/fe11770e8aa72fb818449ea0e30d85d9/jetified-exoplayer-ui-2.10.3/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/fe11770e8aa72fb818449ea0e30d85d9/jetified-exoplayer-ui-2.10.3/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/fe11770e8aa72fb818449ea0e30d85d9/jetified-exoplayer-ui-2.10.3/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/fe11770e8aa72fb818449ea0e30d85d9/jetified-exoplayer-ui-2.10.3/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-ui/2.10.3/707c63f7084ada0b8a18e56be49950365f61048/exoplayer-ui-2.10.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-ui/2.10.3/97545b0e96c33706d1552175476cd82511c16701/exoplayer-ui-2.10.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>