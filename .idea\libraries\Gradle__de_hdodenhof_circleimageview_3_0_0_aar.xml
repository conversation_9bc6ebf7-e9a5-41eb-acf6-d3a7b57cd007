<component name="libraryTable">
  <library name="Gradle: de.hdodenhof:circleimageview:3.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/22919ac3630fad994fcf53de9541627d/jetified-circleimageview-3.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/22919ac3630fad994fcf53de9541627d/jetified-circleimageview-3.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/22919ac3630fad994fcf53de9541627d/jetified-circleimageview-3.0.0/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/de.hdodenhof/circleimageview/3.0.0/****************************************/circleimageview-3.0.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/de.hdodenhof/circleimageview/3.0.0/****************************************/circleimageview-3.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>