<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:keepScreenOn="true">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.wonderkiln.camerakit.CameraView
            android:id="@+id/cameraView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            app:ckFacing="back"
            app:ckLockVideoAspectRatio="true"
            android:keepScreenOn="true"
            app:ckVideoQuality="max480p" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:layout_margin="5dp">

            <com.videostatus.SegmentProgress.SegmentedProgressBar
                android:id="@+id/video_progress"
                android:layout_width="match_parent"
                android:layout_height="5dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:background="@color/fifty_transparent_black" />

        </RelativeLayout>

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="40dp"
            android:background="@color/transparent"
            android:padding="5dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_white_cross" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="40dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_music"
                android:tint="@color/white" />

            <TextView
                android:id="@+id/txt_add_sound"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:text="Add Sound"
                android:textColor="@color/white"
                android:textSize="13dp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/camera_options"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="70dp"
            android:layout_marginRight="15dp"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_rotate_camera"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="@color/transparent"
                android:padding="5dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_rotate_cam" />

            <ImageView
                android:id="@+id/iv_flash_camera"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:background="@color/transparent"
                android:padding="5dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_flash_on" />


        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_alignParentBottom="true"
            android:layout_margin="15dp">

                <LinearLayout
                    android:id="@+id/ly_upload"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/margin_15"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:src="@drawable/ic_gallery_icon" />

                </LinearLayout>


                <ImageView
                    android:id="@+id/iv_record"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_centerInParent="true"
                    android:background="@color/transparent"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_recoding_no" />

                <ImageView
                    android:id="@+id/iv_done"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:background="@color/transparent"
                    android:padding="3dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_not_done" />

        </RelativeLayout>

    </RelativeLayout>

</FrameLayout>
