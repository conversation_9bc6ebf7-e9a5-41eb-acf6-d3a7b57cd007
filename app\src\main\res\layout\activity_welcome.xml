<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:showIn="@layout/activity_welcome">


        <androidx.viewpager.widget.ViewPager
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.viewpagerindicator.LinePageIndicator
            android:id="@+id/line_indictor"
            android:layout_width="150dp"
            android:layout_height="30dp"
            android:foregroundGravity="center_vertical"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="10dp"
            app:selectedColor="@color/colorAccent"
            app:strokeWidth="2dp"
            app:unselectedColor="@color/white" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_above="@id/line_indictor"
            android:alpha=".5"
            android:background="@android:color/white"
            android:visibility="gone" />

        <Button
            android:id="@+id/btn_next"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="10dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/nexy_bg"
            android:text="@string/next"
            android:textAllCaps="false"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/btn_skip"
            android:layout_width="wrap_content"
            android:layout_height="35dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="10dp"
            android:layout_marginBottom="10dp"
            android:background="@null"
            android:text="@string/skip"
            android:textAllCaps="false"
            android:visibility="gone"
            android:textColor="@android:color/white" />

    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>