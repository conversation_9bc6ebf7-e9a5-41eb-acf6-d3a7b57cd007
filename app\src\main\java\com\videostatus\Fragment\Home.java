package com.videostatus.Fragment;

import android.app.AlertDialog;

import androidx.fragment.app.Fragment;

import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.videostatus.Activity.CategoryListActivity;
import com.videostatus.Activity.Latest;
import com.videostatus.Activity.MainActivity;
import com.videostatus.Activity.NewArrival;
import com.videostatus.Activity.UsersListActivity;
import com.videostatus.Adapter.ArtistAdapter;
import com.videostatus.Adapter.BannerAdapter;
import com.videostatus.Adapter.CategoryAdapter;
import com.videostatus.Adapter.LatestAdapter;
import com.videostatus.Adapter.NewArrivalAdapter;
import com.videostatus.Model.BannerModel.BannerModel;
import com.videostatus.Model.CategoryModel.CategoryModel;
import com.videostatus.Model.CategoryModel.Result;
import com.videostatus.Model.TopUsersModel.TopUsersModel;
import com.videostatus.Model.VideoModel.VideoModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.Interface.ShareAll;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;
import com.viewpagerindicator.LinePageIndicator;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class Home extends Fragment implements ShareAll {

    public Home() {
    }

    ProgressDialog progressDialog;

    RecyclerView rv_artist;
    ArtistAdapter artistAdapter;
    List<com.videostatus.Model.TopUsersModel.Result> ArtistList;

    List<Result> CategoryList;
    List<com.videostatus.Model.VideoModel.Result> CategoryVideoList;
    List<com.videostatus.Model.VideoModel.Result> LatestList;
    List<com.videostatus.Model.VideoModel.Result> BannerList;
    List<com.videostatus.Model.VideoModel.Result> NewArrivalList;

    RecyclerView recycler_category, recycler_latest, recycler_popular;
    CategoryAdapter categoryAdapter;

    NewArrivalAdapter newArrivalAdapter;
    LatestAdapter latestAdapter;
    ViewPager mViewPager;
    LinePageIndicator linePageIndicator;
    BannerAdapter bannerAdapter;
    PrefManager prefManager;

    TextView txt_viewall_user, txt_view_all_latest, txt_viewall_category,
            txt_view_all_newarrival;
    Timer timer;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View root = inflater.inflate(R.layout.home, container, false);

        Init(root);

        txt_viewall_user.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(getActivity(), UsersListActivity.class));
            }
        });

        txt_view_all_latest.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(getActivity(), Latest.class));
            }
        });

        txt_viewall_category.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(getActivity(), CategoryListActivity.class));
            }
        });

        txt_view_all_newarrival.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(getActivity(), NewArrival.class));
            }
        });

        DisplayBanner();
        TopUsers();
        Latest();
        Category();
        NewArrival();

        return root;
    }

    public void Init(View root) {
        prefManager = new PrefManager(getActivity());

        progressDialog = new ProgressDialog(getActivity());
        progressDialog.setMessage("Please wait...");
        progressDialog.setCanceledOnTouchOutside(false);

        mViewPager = root.findViewById(R.id.viewPager);
        linePageIndicator = root.findViewById(R.id.line_indictor);

        recycler_category = (RecyclerView) root.findViewById(R.id.recycler_category);
        recycler_latest = (RecyclerView) root.findViewById(R.id.recycler_latest);
        recycler_popular = (RecyclerView) root.findViewById(R.id.recycler_popular);
        rv_artist = (RecyclerView) root.findViewById(R.id.rv_artist);

        txt_viewall_user = root.findViewById(R.id.txt_viewall_user);
        txt_view_all_latest = root.findViewById(R.id.txt_view_all_latest);
        txt_viewall_category = root.findViewById(R.id.txt_viewall_category);
        txt_view_all_newarrival = root.findViewById(R.id.txt_view_all_newarrival);
    }

    @Override
    public void onStart() {
        super.onStart();
        MainActivity.al_title.setVisibility(View.GONE);
        MainActivity.ly_bottom.setBackground(getResources().getDrawable(R.drawable.bottom_gra_bg));
        getActivity().setTitle("Home");
    }

    private void DisplayBanner() {

        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<VideoModel> call = bookNPlayAPI.banner();
        call.enqueue(new Callback<VideoModel>() {
            @Override
            public void onResponse(Call<VideoModel> call, Response<VideoModel> response) {
                progressDialog.dismiss();
                if (response.code() == 200) {
                    BannerList = new ArrayList<>();
                    BannerList = response.body().getResult();
                    Log.e("BannerList", "" + BannerList.size());
                    SetBanner();
                }
            }

            @Override
            public void onFailure(Call<VideoModel> call, Throwable t) {
                progressDialog.dismiss();
            }
        });

    }

    public void SetBanner() {
        bannerAdapter = new BannerAdapter(getActivity(), BannerList);
        mViewPager.setAdapter(bannerAdapter);
        linePageIndicator.setViewPager(mViewPager);

        if (BannerList.size() > 0) {
            TimerTask timerTask = new TimerTask() {
                @Override
                public void run() {
                    mViewPager.post(new Runnable() {
                        @Override
                        public void run() {
                            mViewPager.setCurrentItem((mViewPager.getCurrentItem() + 1) % BannerList.size());
                        }
                    });
                }
            };
            timer = new Timer();
            timer.schedule(timerTask, 10000, 10000);
        }
    }

    private void TopUsers() {
        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<TopUsersModel> call = bookNPlayAPI.TopUsers();
        call.enqueue(new Callback<TopUsersModel>() {
            @Override
            public void onResponse(Call<TopUsersModel> call, Response<TopUsersModel> response) {
                progressDialog.dismiss();
                if (response.code() == 200) {
                    ArtistList = new ArrayList<>();
                    ArtistList = response.body().getResult();

                    if (ArtistList.size() > 0) {
                        artistAdapter = new ArtistAdapter(getActivity(), ArtistList, "");
                        rv_artist.setHasFixedSize(true);
                        RecyclerView.LayoutManager mLayoutManager3 = new LinearLayoutManager(getActivity(),
                                LinearLayoutManager.HORIZONTAL, false);
                        rv_artist.setLayoutManager(mLayoutManager3);
                        rv_artist.setItemAnimator(new DefaultItemAnimator());
                        rv_artist.setAdapter(artistAdapter);
                        artistAdapter.notifyDataSetChanged();
                    }
                }
            }

            @Override
            public void onFailure(Call<TopUsersModel> call, Throwable t) {
                progressDialog.dismiss();
            }
        });
    }

    private void Latest() {

        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<VideoModel> call = bookNPlayAPI.latest_video();
        call.enqueue(new Callback<VideoModel>() {
            @Override
            public void onResponse(Call<VideoModel> call, Response<VideoModel> response) {
                progressDialog.hide();
                if (response.code() == 200) {
                    LatestList = new ArrayList<>();
                    LatestList = response.body().getResult();
                    Log.e("LatestVideoList", "" + LatestList.size());

                    latestAdapter = new LatestAdapter(getActivity(), LatestList, "Home");
                    recycler_latest.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.HORIZONTAL, false));
                    recycler_latest.setItemAnimator(new DefaultItemAnimator());
                    recycler_latest.setAdapter(latestAdapter);
                    latestAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<VideoModel> call, Throwable t) {
                progressDialog.hide();
            }
        });
    }

    private void Category() {

        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<CategoryModel> call = bookNPlayAPI.Category();
        call.enqueue(new Callback<CategoryModel>() {
            @Override
            public void onResponse(Call<CategoryModel> call, Response<CategoryModel> response) {
                progressDialog.hide();
                if (response.code() == 200) {
                    CategoryList = new ArrayList<>();
                    CategoryList = response.body().getResult();
                    Log.e("CategoryList", "" + CategoryList.size());
                    prefManager.CatList = CategoryList;

                    categoryAdapter = new CategoryAdapter(getActivity(), CategoryList, "Home");
                    recycler_category.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.HORIZONTAL, false));
                    recycler_category.setItemAnimator(new DefaultItemAnimator());
                    recycler_category.setAdapter(categoryAdapter);
                    categoryAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<CategoryModel> call, Throwable t) {
                progressDialog.hide();
            }
        });
    }

    private void NewArrival() {

        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<VideoModel> call = bookNPlayAPI.new_arrival();
        call.enqueue(new Callback<VideoModel>() {
            @Override
            public void onResponse(Call<VideoModel> call, Response<VideoModel> response) {
                progressDialog.hide();
                if (response.code() == 200) {
                    NewArrivalList = new ArrayList<>();
                    NewArrivalList = response.body().getResult();
                    Log.e("NewArrivalList", "" + NewArrivalList.size());

                    newArrivalAdapter = new NewArrivalAdapter(getActivity(), NewArrivalList, Home.this);
                    recycler_popular.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.HORIZONTAL, false));
                    recycler_popular.setItemAnimator(new DefaultItemAnimator());
                    recycler_popular.setAdapter(newArrivalAdapter);
                    newArrivalAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<VideoModel> call, Throwable t) {
                progressDialog.hide();
            }
        });
    }

    public void StartDownload(int position) {
    }

    @Override
    public void WhatsappShare() {

    }

    private void alertForApp(String errorMessage, final String packageToRedirect) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getActivity());
        builder.setTitle(R.string.app_not_found);
        builder.setIcon(R.drawable.ic_error);
        builder.setMessage(errorMessage);
        builder.setCancelable(false);
        builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int arg1) {
                String url = getString(R.string.playStore_address) + packageToRedirect;
                Intent i = new Intent(Intent.ACTION_VIEW);
                i.setData(Uri.parse(url));
                startActivity(i);
                dialog.cancel();
            }
        }).setNegativeButton("CANCEl", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int arg1) {
                dialog.cancel();
            }
        });
        AlertDialog alertDialog = builder.create();
        alertDialog.show();

    }

    public void increaseDownload() {
    }
}
