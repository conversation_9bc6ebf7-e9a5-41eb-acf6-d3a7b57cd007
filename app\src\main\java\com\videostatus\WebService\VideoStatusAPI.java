package com.videostatus.WebService;

import com.videostatus.Model.BannerModel.BannerModel;
import com.videostatus.Model.CategoryModel.CategoryModel;
import com.videostatus.Model.CommentModel.CommentModel;
import com.videostatus.Model.FollowModel.FollowModel;
import com.videostatus.Model.GeneralSettings.GeneralSettings;
import com.videostatus.Model.MyFavoriteModel.MyFavoriteModel;
import com.videostatus.Model.ForgotModel.ForgotModel;
import com.videostatus.Model.MyDownloadModel.MyDownloadModel;
import com.videostatus.Model.RegistrationModel.RegistrationModel;
import com.videostatus.Model.RewardModel.RewardModel;
import com.videostatus.Model.SoundModel.SoundModel;
import com.videostatus.Model.SuccessModel.SuccessModel;
import com.videostatus.Model.TopUsersModel.TopUsersModel;
import com.videostatus.Model.UploadVideoModel.UploadVideoModel;
import com.videostatus.Model.UploadedModel.UploadedModel;
import com.videostatus.Model.UserModel.UserModel;
import com.videostatus.Model.VideoDetailsModel.VideoDetailsModel;
import com.videostatus.Model.VideoModel.VideoModel;
import com.videostatus.Model.WithDrawalModel.WithDrawalModel;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;
import retrofit2.http.Query;

public interface VideoStatusAPI {

    @FormUrlEncoded
    @POST("login")
    Call<RegistrationModel> Login(@Field("email") String email, @Field("password") String password);

    @Multipart
    @POST("login")
    Call<RegistrationModel> login(@Part("first_name") RequestBody first_name,
                                  @Part("last_name") RequestBody last_name,
                                  @Part("email") RequestBody email,
                                  @Part("type") RequestBody type,
                                  @Part("mobile_number") RequestBody mobile_number,
                                  @Part("password") RequestBody password,
                                  @Part MultipartBody.Part file);

    @FormUrlEncoded
    @POST("registration")
    Call<RegistrationModel> SignUp(@Field("first_name") String first_name, @Field("last_name") String last_name,
                                   @Field("email") String email, @Field("password") String password,
                                   @Field("mobile_number") String mobile_number);

    @GET("get_category")
    Call<CategoryModel> Category();

    @GET("top_users")
    Call<TopUsersModel> TopUsers();

    @GET("get_related_video")
    Call<VideoModel> related_video(@Query("cat_id") String cat_id);

    @GET("latest_video")
    Call<VideoModel> latest_video();

    @GET("popular_video")
    Call<VideoModel> popular_video();

    @GET("get_new_arrival")
    Call<VideoModel> new_arrival();

    @GET("get_video_detail")
    Call<VideoDetailsModel> get_video_detail(@Query("id") String id);

    @GET("get_user_list")
    Call<UserModel> get_user_list(@Query("loginid") String loginid);

    @GET("mydowanload_video")
    Call<MyDownloadModel> mydowanload_video(@Query("user_id") String user_id);


    @GET("add_dowanload")
    Call<SuccessModel> add_dowanload(@Query("v_id") String video_id,
                                 @Query("user_id") String user_id);

    @GET("favorite_list")
    Call<MyFavoriteModel> favorite_list(@Query("user_id") String user_id);

    @GET("uploaded_video_list")
    Call<VideoModel> uploaded_video_list(@Query("user_id") String user_id,
                                            @Query("page_no") String page_no);

    @GET("uservideo")
    Call<VideoModel> uservideo(@Query("user_id") String user_id);

    @GET("get_banner_video")
    Call<VideoModel> banner();

    @FormUrlEncoded
    @POST("forgot_password")
    Call<SuccessModel> forgotpassword(@Field("email") String email);

    @GET("profile")
    Call<UserModel> profile(@Query("user_id") String user_id,
                            @Query("to_user_id") String to_user_id);

    @GET("video_view")
    Call<SuccessModel> video_view(@Query("id") String id);

    @Multipart
    @POST("upload_video")
    Call<UploadVideoModel> upload_video(@Part MultipartBody.Part file,
                                        @Part MultipartBody.Part file2,
                                        @Part("fb_id") RequestBody fb_id,
                                        @Part("video_title") RequestBody video_title,
                                        @Part("video_category") RequestBody video_category,
                                        @Part("video_duration") RequestBody video_duration,
                                        @Part("sound_id") RequestBody sound_id);

    @GET("genaral_setting")
    Call<GeneralSettings> genaral_setting();

    @GET("get_rewards")
    Call<RewardModel> get_rewards(@Query("user_id") String user_id);

    @GET("withdrawal_list")
    Call<WithDrawalModel> withdrawal_list(@Query("user_id") String user_id);

    @GET("top_users")
    Call<UserModel> top_users();

    @GET("view_comment")
    Call<CommentModel> view_comment(@Query("v_id") String v_id);

    @FormUrlEncoded
    @POST("add_comment")
    Call<SuccessModel> add_comment(@Field("v_id") String v_id,
                                   @Field("user_id") String user_id,
                                   @Field("comment") String comment);

    @FormUrlEncoded
    @POST("like_dislike")
    Call<SuccessModel> like_dislike(@Field("v_id") String v_id,
                                    @Field("user_id") String user_id,
                                    @Field("status") String status);

    @FormUrlEncoded
    @POST("add_ratings")
    Call<SuccessModel> add_ratings(@Field("v_id") String v_id,
                                   @Field("user_id") String user_id,
                                   @Field("rating") String status);

    @FormUrlEncoded
    @POST("add_favorite")
    Call<SuccessModel> add_favorite(@Field("v_id") String v_id,
                                    @Field("user_id") String user_id);

    @FormUrlEncoded
    @POST("add_earnpoint")
    Call<SuccessModel> add_earnpoint(@Field("user_id") String user_id,
                                     @Field("type") String type);

    @FormUrlEncoded
    @POST("withdrawal_request")
    Call<SuccessModel> withdrawal_request(@Field("user_id") String user_id,
                                          @Field("payment_type") String payment_type,
                                          @Field("payment_detail") String payment_detail);

    @GET("share_video")
    Call<SuccessModel> share_video(@Query("id") String id);

    @GET("follow")
    Call<SuccessModel> follow(@Query("user_id") String user_id,
                              @Query("to_user_id") String to_user_id);

    @GET("follow_list")
    Call<FollowModel> follow_list(@Query("user_id") String user_id);

    @GET("following_list")
    Call<FollowModel> following_list(@Query("user_id") String user_id);

    @GET("get_sound")
    Call<SoundModel> get_sound();

    @GET("get_following_video")
    Call<VideoModel> get_following_video(@Query("user_id") String user_id);

    @GET("get_like_video")
    Call<VideoModel> get_like_video(@Query("user_id") String user_id);

}
