<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gray_light"
        android:orientation="vertical">

        <ir.alirezabdn.wp7progress.WP10ProgressBar
            android:id="@+id/wp7progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            app:indicatorColor="@color/colorAccent"
            app:indicatorHeight="7"
            app:indicatorRadius="5"
            app:interval="100" />

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/swipeContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_category"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="4dp"
                    android:nestedScrollingEnabled="false"
                    android:scrollbars="none"
                    android:visibility="visible" />
            </LinearLayout>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>