package com.videostatus.Activity;

import android.app.ProgressDialog;
import android.content.Intent;
import android.graphics.PorterDuff;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.videostatus.Adapter.DownloadAdapter;
import com.videostatus.Model.MyDownloadModel.MyDownloadModel;
import com.videostatus.Model.MyDownloadModel.Result;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.Interface.ShareAll;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class DownloadStatus extends AppCompatActivity implements ShareAll {

    public DownloadStatus() {
    }

    RecyclerView recycler_download;
    List<Result> downloadList;
    DownloadAdapter downloadAdapter;
    String id;
    ProgressDialog progressDialog;
    PrefManager prefManager;
    TextView txt_no_record;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.download_status);

        Toolbar toolbar = (Toolbar) findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Download Status");

        toolbar.setTitleTextColor(getResources().getColor(R.color.colorPrimary));
        toolbar.getNavigationIcon().setColorFilter(getResources().getColor(R.color.colorPrimary), PorterDuff.Mode.SRC_ATOP);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowHomeEnabled(true);

        prefManager = new PrefManager(DownloadStatus.this);

        progressDialog = new ProgressDialog(DownloadStatus.this);
        progressDialog.setMessage("Please wait...");
        progressDialog.setCanceledOnTouchOutside(false);

        recycler_download = (RecyclerView) findViewById(R.id.recycler_download);
        txt_no_record = (TextView) findViewById(R.id.txt_no_record);

        if (!prefManager.getLoginId().equalsIgnoreCase("0"))
            mydowanload_video();
        else {
            startActivity(new Intent(DownloadStatus.this, LoginActivity.class));
            finish();
        }

    }

    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void mydowanload_video() {

        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<MyDownloadModel> call = bookNPlayAPI.mydowanload_video(prefManager.getLoginId());
        call.enqueue(new Callback<MyDownloadModel>() {
            @Override
            public void onResponse(Call<MyDownloadModel> call, Response<MyDownloadModel> response) {
                progressDialog.hide();
                if (response.code() == 200) {
                    downloadList = new ArrayList<>();
                    downloadList = response.body().getResult();
                    Log.e("downloadList", "" + downloadList.size());

                    if (downloadList.size() > 0) {
                        downloadAdapter = new DownloadAdapter(DownloadStatus.this, downloadList, DownloadStatus.this);
//                        recycler_download.setLayoutManager(new LinearLayoutManager(DownloadStatus.this, LinearLayoutManager.VERTICAL, false));
                        GridLayoutManager gridLayoutManager = new GridLayoutManager(getApplicationContext(), 2);
                        recycler_download.setLayoutManager(gridLayoutManager);
                        recycler_download.setItemAnimator(new DefaultItemAnimator());
                        recycler_download.setAdapter(downloadAdapter);
                        downloadAdapter.notifyDataSetChanged();
                        txt_no_record.setVisibility(View.GONE);
                        recycler_download.setVisibility(View.VISIBLE);
                    } else {
                        txt_no_record.setVisibility(View.VISIBLE);
                        txt_no_record.setText("You have not download any video yet");
                        recycler_download.setVisibility(View.GONE);
                    }
                } else if (response.code() == 400) {
                    txt_no_record.setVisibility(View.VISIBLE);
                    txt_no_record.setText("You have not download any video yet");
                    recycler_download.setVisibility(View.GONE);
                }
            }

            @Override
            public void onFailure(Call<MyDownloadModel> call, Throwable t) {
                progressDialog.hide();
            }
        });
    }

    @Override
    public void WhatsappShare() {

    }
}
