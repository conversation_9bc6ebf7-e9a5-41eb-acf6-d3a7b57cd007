package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.iarcuschin.simpleratingbar.SimpleRatingBar;
import com.squareup.picasso.Picasso;
import com.videostatus.Activity.StatusDetails;
import com.videostatus.Model.MyDownloadModel.Result;
import com.videostatus.R;
import com.videostatus.Interface.ShareAll;

import java.util.List;

public class DownloadAdapter extends RecyclerView.Adapter<DownloadAdapter.MyViewHolder> {

    private List<Result> DownloadList;
    Context mcontext;

    ShareAll shareAll;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        TextView txt_title, txt_bookmark, txt_view, txt_avg;
        ImageView iv_thumb;
        CardView cardview;
        SimpleRatingBar ratingbar;

        public MyViewHolder(View view) {
            super(view);
            txt_bookmark = view.findViewById(R.id.txt_bookmark);
            iv_thumb = view.findViewById(R.id.iv_thumb);
            txt_title = view.findViewById(R.id.txt_title);
            txt_view = view.findViewById(R.id.txt_view);
            txt_avg = view.findViewById(R.id.txt_avg);
            cardview = view.findViewById(R.id.cardview);
            ratingbar = view.findViewById(R.id.ratingbar);
        }
    }

    public DownloadAdapter(Context context, List<Result> DownloadList, ShareAll shareAll) {
        this.DownloadList = DownloadList;
        this.mcontext = context;
        this.shareAll = shareAll;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.down_item_row, parent, false);

        return new MyViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {
        holder.txt_title.setText(DownloadList.get(position).getVideoTitle());
//        holder.txt_view.setText(withSuffix(Long.parseLong("" + DownloadList.get(position).getView())) + "");

        Picasso.with(mcontext).load(DownloadList.get(position).getThumbnailImg()).resize(400, 400)
                .placeholder(R.drawable.no_image).centerInside().into(holder.iv_thumb);

        holder.ratingbar.setRating(Float.parseFloat(DownloadList.get(position).getAvgRating()));
        holder.txt_avg.setText(" " + DownloadList.get(position).getAvgRating());

        holder.txt_view.setText(" "+withSuffix(Long.parseLong(""+DownloadList.get(position).getView()))+" Views");

        holder.txt_bookmark.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
            }
        });

        holder.cardview.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(mcontext, StatusDetails.class);
                intent.putExtra("id",""+DownloadList.get(position).getId());
                mcontext.startActivity(intent);
            }
        });
    }

    @Override
    public int getItemCount() {
        return DownloadList.size();
    }

    public String withSuffix(long count) {
        if (count < 1000) return "" + count;
        int exp = (int) (Math.log(count) / Math.log(1000));
        return String.format("%.1f %c",
                count / Math.pow(1000, exp),
                "kMGTPE".charAt(exp - 1));
    }

}
