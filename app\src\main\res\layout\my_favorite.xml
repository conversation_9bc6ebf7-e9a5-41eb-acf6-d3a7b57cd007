<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white"
            app:titleTextColor="@color/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <ir.alirezabdn.wp7progress.WP10ProgressBar
                android:id="@+id/wp7progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                app:indicatorColor="@color/colorAccent"
                app:indicatorHeight="7"
                app:indicatorRadius="5"
                app:interval="100" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_fav"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:nestedScrollingEnabled="false"
                    android:scrollbars="none"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/txt_no_record"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/spacing_medium"
                    android:fontFamily="@font/public_medium"
                    android:gravity="center"
                    android:text="You have not upload any video yet"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16dp"
                    android:visibility="gone" />
            </LinearLayout>
        </RelativeLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>