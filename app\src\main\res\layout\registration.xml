<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_login_patter"
        android:orientation="vertical"
        android:weightSum="1.0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="0.35"
            android:gravity="center|bottom"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="70dp"
                android:src="@drawable/login_logo" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:layout_weight="0.5"
            android:gravity="center"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="@drawable/edittext_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:weightSum="1.0">


                <EditText
                    android:id="@+id/et_fullname"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="5dp"
                    android:background="@null"
                    android:fontFamily="@font/public_medium"
                    android:hint="First Name"
                    android:paddingLeft="5dp"
                    android:singleLine="true"
                    android:textColor="@color/font_dark"
                    android:textColorHint="@color/font_light"
                    android:textSize="14dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/edittext_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:weightSum="1.0">

                <EditText
                    android:id="@+id/et_lastname"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="5dp"
                    android:background="@null"
                    android:fontFamily="@font/public_medium"
                    android:hint="Last Name"
                    android:paddingLeft="5dp"
                    android:singleLine="true"
                    android:textColor="@color/font_dark"
                    android:textColorHint="@color/font_light"
                    android:textSize="14dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/edittext_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:weightSum="1.0">

                <EditText
                    android:id="@+id/et_email"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="5dp"
                    android:background="@null"
                    android:fontFamily="@font/public_medium"
                    android:hint="Email"
                    android:paddingLeft="5dp"
                    android:singleLine="true"
                    android:textColor="@color/font_dark"
                    android:textColorHint="@color/font_light"
                    android:textSize="14dp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/edittext_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:weightSum="1.0">

                <EditText
                    android:id="@+id/et_password"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="5dp"
                    android:background="@null"
                    android:fontFamily="@font/public_medium"
                    android:hint="Password"
                    android:inputType="textPassword"
                    android:paddingLeft="5dp"
                    android:singleLine="true"
                    android:textColor="@color/font_dark"
                    android:textColorHint="@color/font_light"
                    android:textSize="14dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/edittext_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:weightSum="1.0">

                <EditText
                    android:id="@+id/et_phone"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="5dp"
                    android:background="@null"
                    android:fontFamily="@font/public_medium"
                    android:hint="Phone"
                    android:inputType="number"
                    android:paddingLeft="5dp"
                    android:singleLine="true"
                    android:textColor="@color/font_dark"
                    android:textColorHint="@color/font_light"
                    android:textSize="14dp" />
            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="0.2"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:weightSum="1.0">

                <TextView
                    android:id="@+id/txt_signup"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/round_bor_bg"
                    android:fontFamily="@font/public_medium"
                    android:gravity="center"
                    android:text="SIGN UP"
                    android:textColor="@color/white"
                    android:textSize="14dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:letterSpacing="0.05"
                        android:text="Already have an account Yet?"
                        android:textColor="@color/font_dark"
                        android:textSize="12dp" />

                    <TextView
                        android:id="@+id/txt_registration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center_vertical"
                        android:text=" Login"
                        android:textColor="@color/colorPrimary"
                        android:textSize="12dp" />
                </LinearLayout>


            </LinearLayout>
        </LinearLayout>


    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>