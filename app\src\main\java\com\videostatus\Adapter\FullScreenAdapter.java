package com.videostatus.Adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.iarcuschin.simpleratingbar.SimpleRatingBar;
import com.squareup.picasso.Picasso;
import com.videostatus.Activity.StatusDetails;
import com.videostatus.Interface.OnItemClickListener;
import com.videostatus.Model.VideoModel.Result;
import com.videostatus.R;
import com.videostatus.Utility.Constant;

import java.util.List;

import de.hdodenhof.circleimageview.CircleImageView;

public class FullScreenAdapter extends RecyclerView.Adapter<FullScreenAdapter.MyViewHolder> {

    private List<Result> LatestList;
    Context mcontext;
    String from;
    private OnItemClickListener listener;
    int share_cnt, like_cnt;


    public class MyViewHolder extends RecyclerView.ViewHolder {
        TextView txt_username, txt_following, txt_related, txt_related_select, txt_following_select;
        TextView txt_whatsapp, txt_insta, txt_facebook, txt_sound_name, txt_like, txt_comment, txt_share;
        LinearLayout ly_shared_layout, ly_like_layout, ly_comment_layout, ly_sound;
        CircleImageView iv_user_pic;
        ImageView iv_like_image, iv_add_follow;

        public MyViewHolder(View view) {
            super(view);
            txt_username = view.findViewById(R.id.txt_username);
            txt_whatsapp = view.findViewById(R.id.txt_whatsapp);
            txt_insta = view.findViewById(R.id.txt_insta);
            txt_facebook = view.findViewById(R.id.txt_facebook);
            txt_sound_name = view.findViewById(R.id.txt_sound_name);
            txt_like = view.findViewById(R.id.txt_like);
            txt_comment = view.findViewById(R.id.txt_comment);
            txt_share = view.findViewById(R.id.txt_share);
            txt_following = view.findViewById(R.id.txt_following);
            txt_related = view.findViewById(R.id.txt_related);

            ly_sound = view.findViewById(R.id.ly_sound);
            ly_shared_layout = view.findViewById(R.id.ly_shared_layout);
            ly_like_layout = view.findViewById(R.id.ly_like_layout);
            ly_comment_layout = view.findViewById(R.id.ly_comment_layout);
            iv_user_pic = view.findViewById(R.id.iv_user_pic);

            iv_like_image = view.findViewById(R.id.iv_like_image);
            iv_add_follow = view.findViewById(R.id.iv_add_follow);

            txt_related_select = view.findViewById(R.id.txt_related_select);
            txt_following_select = view.findViewById(R.id.txt_following_select);

        }

        public void bind(final int postion, final Result item,
                         final OnItemClickListener listener) {

            txt_following.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Constant.is_follow_related = 1;
                    txt_related_select.setVisibility(View.INVISIBLE);
                    txt_following_select.setVisibility(View.VISIBLE);
                    listener.onItemClick(postion, item, v);
                }
            });

            txt_related.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Constant.is_follow_related = 2;
                    txt_related_select.setVisibility(View.VISIBLE);
                    txt_following_select.setVisibility(View.INVISIBLE);
                    listener.onItemClick(postion, item, v);
                }
            });

            txt_username.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(postion, item, v);
                }
            });

            txt_whatsapp.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(postion, item, v);
                }
            });

            txt_insta.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(postion, item, v);
                }
            });

            txt_facebook.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(postion, item, v);
                }
            });

            ly_shared_layout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    share_cnt = (Integer.parseInt(LatestList.get(postion).getTotalShare()) + 1);
                    LatestList.get(postion).setTotalShare("" + share_cnt);
                    txt_share.setText("" + withSuffix(Long.parseLong(LatestList.get(postion).getTotalShare())));
                    listener.onItemClick(postion, item, v);
                }
            });

            ly_like_layout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(postion, item, v);
                }
            });

            ly_comment_layout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(postion, item, v);
                }
            });

            iv_user_pic.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(postion, item, v);
                }
            });

            iv_add_follow.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(postion, item, v);
                }
            });

        }
    }

    public FullScreenAdapter(Context context, List<Result> moviesList, String from,
                             OnItemClickListener listener) {
        this.LatestList = moviesList;
        this.mcontext = context;
        this.from = from;
        this.listener = listener;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.fullscreen_item_row, parent, false);
        return new MyViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        Result result = LatestList.get(position);
        holder.setIsRecyclable(false);

        holder.bind(position, result, listener);

        holder.txt_username.setText(LatestList.get(position).getVideoTitle());
        holder.txt_sound_name.setSelected(true);
        holder.txt_sound_name.setText("" + LatestList.get(position).getSoundTitle());

        holder.txt_like.setText("" + withSuffix(Long.parseLong(LatestList.get(position).getTotalLike())));
        holder.txt_comment.setText("" + withSuffix(Long.parseLong(LatestList.get(position).getTotalComment())));
        holder.txt_share.setText("" + withSuffix(Long.parseLong(LatestList.get(position).getTotalShare())));

        listener.onItemClick(position, result, holder.txt_like);

        if (Constant.is_follow_related == 0) {
            holder.txt_following_select.setVisibility(View.INVISIBLE);
            holder.txt_related_select.setVisibility(View.INVISIBLE);
        } else if (Constant.is_follow_related == 1) {
            holder.txt_following_select.setVisibility(View.VISIBLE);
            holder.txt_related_select.setVisibility(View.INVISIBLE);
        } else if (Constant.is_follow_related == 2) {
            holder.txt_following_select.setVisibility(View.INVISIBLE);
            holder.txt_related_select.setVisibility(View.VISIBLE);
        }

        if (!LatestList.get(position).getSoundTitle().isEmpty()) {
            holder.ly_sound.setVisibility(View.VISIBLE);
        } else {
            holder.ly_sound.setVisibility(View.GONE);
        }

        if (!LatestList.get(position).getProfileImg().isEmpty()) {

            Picasso.with(mcontext).load(LatestList.get(position).getProfileImg()).resize(400, 400)
                    .placeholder(R.drawable.no_user).centerInside().into(holder.iv_user_pic);
        }

        if (LatestList.get(position).getIsLike().equalsIgnoreCase("1")) {
            holder.iv_like_image.setBackground(mcontext.getResources().getDrawable(R.drawable.ic_like_fill));
        } else {
            holder.iv_like_image.setBackground(mcontext.getResources().getDrawable(R.drawable.ic_like));
        }

        if (LatestList.get(position).getIs_follow().equalsIgnoreCase("1")) {
            holder.iv_add_follow.setBackground(mcontext.getResources().getDrawable(R.drawable.ic_follow));
        } else {
            holder.iv_add_follow.setBackground(mcontext.getResources().getDrawable(R.drawable.ic_add_follow));
        }

    }

    @Override
    public int getItemCount() {
        return LatestList.size();
    }

    public String withSuffix(long count) {
        if (count < 1000) return "" + count;
        int exp = (int) (Math.log(count) / Math.log(1000));
        return String.format("%.1f %c",
                count / Math.pow(1000, exp),
                "kMGTPE".charAt(exp - 1));
    }
}
