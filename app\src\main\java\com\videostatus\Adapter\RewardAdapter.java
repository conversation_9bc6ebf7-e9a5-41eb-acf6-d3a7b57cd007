package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.squareup.picasso.Picasso;
import com.videostatus.Activity.VideoList;
import com.videostatus.Model.RewardModel.Result;
import com.videostatus.R;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class RewardAdapter extends RecyclerView.Adapter<RewardAdapter.MyViewHolder> {

    private List<Result> RewardList;
    Context mcontext;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        public TextView txt_title, txt_date, txt_desc, txt_points;
        ImageView iv_thumb;

        public MyViewHolder(View view) {
            super(view);
            txt_title = view.findViewById(R.id.txt_title);
            txt_date = view.findViewById(R.id.txt_date);
            txt_desc = view.findViewById(R.id.txt_desc);
            txt_points = view.findViewById(R.id.txt_points);
            iv_thumb = view.findViewById(R.id.iv_thumb);
        }
    }


    public RewardAdapter(Context context, List<Result> RewardList) {
        this.RewardList = RewardList;
        this.mcontext = context;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.reward_item_row, parent, false);

        return new MyViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        holder.txt_title.setText(RewardList.get(position).getReason());
        holder.txt_points.setText(""+RewardList.get(position).getPoint()+" Points");

        try {
            SimpleDateFormat dateFormat = dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            Date sourceDate = sourceDate = dateFormat.parse(RewardList.get(position).getCreatedAt());
            SimpleDateFormat targetFormat = new SimpleDateFormat("yy-MMM-dd HH:mm: a");
            String targetdatevalue = targetFormat.format(sourceDate);
            holder.txt_date.setText("" + targetdatevalue);
        } catch (Exception e) {
            Log.e("Exception", "" + e.getMessage());
        }

//        Picasso.with(mcontext).load(RewardList.get(position).get).fit()
//                .into(holder.iv_thumb);

        holder.iv_thumb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Log.e("click", "call");
                Intent intent = new Intent(mcontext, VideoList.class);
                intent.putExtra("Id", RewardList.get(position).getId());
                mcontext.startActivity(intent);
            }
        });
    }

    @Override
    public int getItemCount() {
        return RewardList.size();
    }

}
