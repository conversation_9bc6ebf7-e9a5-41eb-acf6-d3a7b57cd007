package com.videostatus.Activity;

import android.app.ProgressDialog;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.tabs.TabLayout;
import com.makeramen.roundedimageview.RoundedImageView;
import com.squareup.picasso.Picasso;
import com.videostatus.Adapter.FollowerAdapter;
import com.videostatus.Adapter.UploadedAdapter;
import com.videostatus.Model.FollowModel.FollowModel;
import com.videostatus.Model.SuccessModel.SuccessModel;
import com.videostatus.Model.UserModel.Result;
import com.videostatus.Model.UserModel.UserModel;
import com.videostatus.Model.VideoModel.VideoModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.Interface.ShareAll;
import com.videostatus.Utility.Utils;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class Profile extends AppCompatActivity implements ShareAll {

    RecyclerView recycler_uploaded;

    UploadedAdapter uploadedAdapter;
    FollowerAdapter followerAdapter;

    TextView txt_location, txt_name, txt_instagram_url, txt_follower,
            txt_following, txt_no_record;
    ProgressDialog progressDialog;
    PrefManager prefManager;

    List<Result> ProfileList;
    List<com.videostatus.Model.VideoModel.Result> UploadedList;
    List<com.videostatus.Model.FollowModel.Result> FollowerList;
    List<com.videostatus.Model.FollowModel.Result> FollowingList;
    List<com.videostatus.Model.VideoModel.Result> LikeVideoList;
    List<com.videostatus.Model.VideoModel.Result> CommentVideoList;
    RoundedImageView iv_user_pic;
    String User_Id, TO_Id;

    RecyclerView recycler_followers, recycler_following, recycler_like, recycler_comments;
    private TabLayout tabLayout;
    TextView txt_coin, txt_follow;
    LinearLayout txt_back;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.profile);

        Init();

        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
//            User_Id = bundle.getString("Id");
            User_Id = prefManager.getLoginId();
            TO_Id = bundle.getString("TO_Id");

            Profile();
            Followers();
            Following();
            Uploaded_Video();
            GetLikeVideo();
            GetCommentVideo();

        }

        txt_back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });

        txt_follow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!prefManager.getLoginId().equalsIgnoreCase("0"))
                    Follow();
                else
                    startActivity(new Intent(Profile.this, LoginActivity.class));
            }
        });

        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                switch (tab.getPosition()) {
                    case 0:
                        Log.e("==>", "" + tab.getPosition());
                        findViewById(R.id.ly_second).setVisibility(View.GONE);
                        recycler_uploaded.setVisibility(View.VISIBLE);
                        SetUploadVideo();
                        break;

                    case 1:
                        Log.e("==>", "" + tab.getPosition());
                        findViewById(R.id.ly_second).setVisibility(View.VISIBLE);
                        recycler_uploaded.setVisibility(View.GONE);
                        SetLikeVideo();
                        SetCommentVideo();
                        break;
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {

            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

    }

    public void Init() {

        prefManager = new PrefManager(Profile.this);

        progressDialog = new ProgressDialog(Profile.this);
        progressDialog.setMessage("Please wait...");
        progressDialog.setCanceledOnTouchOutside(false);

        iv_user_pic = findViewById(R.id.iv_user_pic);
        txt_name = findViewById(R.id.txt_name);
        txt_location = findViewById(R.id.txt_location);
        txt_instagram_url = findViewById(R.id.txt_instagram_url);
        txt_follower = findViewById(R.id.txt_follower);
        txt_following = findViewById(R.id.txt_following);

        txt_follow = findViewById(R.id.txt_follow);
        txt_coin = findViewById(R.id.txt_coin);
        txt_back = findViewById(R.id.txt_back);

        txt_no_record = findViewById(R.id.txt_no_record);

        recycler_uploaded = findViewById(R.id.recycler_uploaded);
        recycler_followers = findViewById(R.id.recycler_followers);
        recycler_following = findViewById(R.id.recycler_following);
        recycler_like = findViewById(R.id.recycler_like);
        recycler_comments = findViewById(R.id.recycler_comment);

        this.tabLayout = findViewById(R.id.tabLayout);

        tabLayout.addTab(tabLayout.newTab().setText("Posts"));
        tabLayout.addTab(tabLayout.newTab().setText("Activity"));
//        tabLayout.addTab(tabLayout.newTab().setText("Bio"));

    }

    @Override
    public void WhatsappShare() {

    }

    private void Profile() {
        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<UserModel> call = bookNPlayAPI.profile(User_Id, TO_Id);
        call.enqueue(new Callback<UserModel>() {
            @Override
            public void onResponse(Call<UserModel> call, Response<UserModel> response) {
                progressDialog.dismiss();
                if (response.code() == 200) {
                    ProfileList = new ArrayList<>();
                    ProfileList = response.body().getResult();
                    if (ProfileList.size() > 0) {

                        txt_name.setText("" + ProfileList.get(0).getFullname());
                        txt_location.setText("" + ProfileList.get(0).getLocation());
                        txt_instagram_url.setText("" + ProfileList.get(0).getInstagram_url());
                        txt_coin.setText("" + Utils.withSuffix(Long.parseLong(ProfileList.get(0).getTotalPoints())));
                        if (ProfileList.get(0).getIsFollow().equalsIgnoreCase("0"))
                            txt_follow.setText("Follow");
                        else
                            txt_follow.setText("Unfollow");

                        if (!ProfileList.get(0).getProfileImg().isEmpty())
                            Picasso.with(Profile.this).load(ProfileList.get(0).getProfileImg())
                                    .placeholder(R.drawable.no_user)
                                    .into(iv_user_pic);
                    }
                }
            }

            @Override
            public void onFailure(Call<UserModel> call, Throwable t) {
                progressDialog.dismiss();
            }
        });
    }

    private void Followers() {

        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<FollowModel> call = bookNPlayAPI.follow_list("" + TO_Id);
        call.enqueue(new Callback<FollowModel>() {
            @Override
            public void onResponse(Call<FollowModel> call, Response<FollowModel> response) {
                if (response.code() == 200) {
                    if (response.body().getStatus() == 200) {
                        FollowerList = new ArrayList<>();
                        FollowerList = response.body().getResult();
                        Log.e("FollowerList", "" + FollowerList.size());

                        if (FollowerList.size() > 0) {
                            Log.e("LatestList_tmp", "" + FollowerList.size());

                            txt_follower.setText("Followers ( " + Utils.withSuffix(Long.parseLong("" + FollowerList.size())) + " )");

                            followerAdapter = new FollowerAdapter(Profile.this, FollowerList, Profile.this);
                            recycler_followers.setLayoutManager(new LinearLayoutManager(Profile.this,
                                    LinearLayoutManager.HORIZONTAL, false));
                            recycler_followers.setAdapter(followerAdapter);

                            recycler_followers.setVisibility(View.VISIBLE);
                            txt_follower.setVisibility(View.VISIBLE);
                        } else {
                            recycler_followers.setVisibility(View.GONE);
                            txt_follower.setVisibility(View.GONE);
                        }
                    } else {
                        recycler_followers.setVisibility(View.GONE);
                        txt_follower.setVisibility(View.GONE);
                    }
                } else {
                    recycler_followers.setVisibility(View.GONE);
                    txt_follower.setVisibility(View.GONE);
                }
            }

            @Override
            public void onFailure(Call<FollowModel> call, Throwable t) {
            }
        });
    }

    private void Following() {

        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<FollowModel> call = bookNPlayAPI.following_list("" + TO_Id);
        call.enqueue(new Callback<FollowModel>() {
            @Override
            public void onResponse(Call<FollowModel> call, Response<FollowModel> response) {
                if (response.code() == 200) {
                    if (response.body().getStatus() == 200) {
                        FollowingList = new ArrayList<>();
                        FollowingList = response.body().getResult();
                        Log.e("FollowingList", "" + FollowingList.size());

                        if (FollowingList.size() > 0) {
                            Log.e("FollowingList", "" + FollowingList.size());

                            txt_following.setText("Following ( " + Utils.withSuffix(Long.parseLong("" + FollowingList.size())) + " )");

                            followerAdapter = new FollowerAdapter(Profile.this, FollowingList, Profile.this);
                            recycler_following.setLayoutManager(new LinearLayoutManager(Profile.this,
                                    LinearLayoutManager.HORIZONTAL, false));
                            recycler_following.setAdapter(followerAdapter);

                            recycler_following.setVisibility(View.VISIBLE);
                            txt_following.setVisibility(View.VISIBLE);
                        } else {
                            recycler_following.setVisibility(View.GONE);
                            txt_following.setVisibility(View.GONE);
                        }
                    } else {
                        recycler_following.setVisibility(View.GONE);
                        txt_following.setVisibility(View.GONE);
                    }
                } else {
                    recycler_following.setVisibility(View.GONE);
                    txt_following.setVisibility(View.GONE);
                }
            }

            @Override
            public void onFailure(Call<FollowModel> call, Throwable t) {
            }
        });
    }

    /* Upload Video */

    private void Uploaded_Video() {

        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<VideoModel> call = bookNPlayAPI.uservideo("" + TO_Id);
        call.enqueue(new Callback<VideoModel>() {
            @Override
            public void onResponse(Call<VideoModel> call, Response<VideoModel> response) {
                UploadedList = new ArrayList<>();
                if (response.code() == 200) {
                    if (response.body().getStatus() == 200) {
                        UploadedList = response.body().getResult();
                        Log.e("UploadedList", "" + UploadedList.size());
                        SetUploadVideo();
                    } else {
                        recycler_uploaded.setVisibility(View.GONE);
                        txt_no_record.setVisibility(View.VISIBLE);
                    }
                }
            }

            @Override
            public void onFailure(Call<VideoModel> call, Throwable t) {
                recycler_uploaded.setVisibility(View.GONE);
            }
        });
    }

    private void SetUploadVideo() {
        if (UploadedList.size() > 0) {
            Log.e("LatestList_tmp", "" + UploadedList.size());
            uploadedAdapter = new UploadedAdapter(Profile.this, UploadedList, Profile.this);
            GridLayoutManager gridLayoutManager = new GridLayoutManager(Profile.this, 3);
            recycler_uploaded.setLayoutManager(gridLayoutManager);
            recycler_uploaded.setAdapter(uploadedAdapter);
            uploadedAdapter.notifyDataSetChanged();

            recycler_uploaded.setVisibility(View.VISIBLE);
            txt_no_record.setVisibility(View.GONE);
        } else {
            recycler_uploaded.setVisibility(View.GONE);
            txt_no_record.setVisibility(View.VISIBLE);
        }
    }

    /* Get Like Video and Set in Adater */

    private void GetLikeVideo() {

        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<VideoModel> call = bookNPlayAPI.get_like_video("" + TO_Id);
        call.enqueue(new Callback<VideoModel>() {
            @Override
            public void onResponse(Call<VideoModel> call, Response<VideoModel> response) {
                LikeVideoList = new ArrayList<>();
                if (response.code() == 200) {
                    if (response.body().getStatus() == 200) {
                        LikeVideoList = response.body().getResult();
                        Log.e("LiveVideoList", "" + LikeVideoList.size());
                    }
                }
            }

            @Override
            public void onFailure(Call<VideoModel> call, Throwable t) {
                recycler_uploaded.setVisibility(View.GONE);
            }
        });
    }

    private void SetLikeVideo() {
        if (LikeVideoList.size() > 0) {
            Log.e("LatestList_tmp", "" + LikeVideoList.size());
            uploadedAdapter = new UploadedAdapter(Profile.this, LikeVideoList, Profile.this);
            GridLayoutManager gridLayoutManager = new GridLayoutManager(Profile.this, 3);
            recycler_like.setLayoutManager(gridLayoutManager);
            recycler_like.setAdapter(uploadedAdapter);
            uploadedAdapter.notifyDataSetChanged();

            recycler_like.setVisibility(View.VISIBLE);
            findViewById(R.id.txt_like).setVisibility(View.VISIBLE);
            txt_no_record.setVisibility(View.GONE);
        } else {
            recycler_like.setVisibility(View.GONE);
            findViewById(R.id.txt_like).setVisibility(View.GONE);
            txt_no_record.setVisibility(View.VISIBLE);
        }
    }

    /* Get Comment Video and Set in Adater */

    private void GetCommentVideo() {

        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<VideoModel> call = bookNPlayAPI.get_like_video("" + TO_Id);
        call.enqueue(new Callback<VideoModel>() {
            @Override
            public void onResponse(Call<VideoModel> call, Response<VideoModel> response) {
                CommentVideoList = new ArrayList<>();
                if (response.code() == 200) {
                    if (response.body().getStatus() == 200) {
                        CommentVideoList = response.body().getResult();
                        Log.e("CommentvideoList", "" + CommentVideoList.size());
                    }
                }
            }

            @Override
            public void onFailure(Call<VideoModel> call, Throwable t) {
                recycler_uploaded.setVisibility(View.GONE);
            }
        });
    }

    private void SetCommentVideo() {
        if (CommentVideoList.size() > 0) {
            Log.e("LatestList_tmp", "" + CommentVideoList.size());
            uploadedAdapter = new UploadedAdapter(Profile.this, CommentVideoList, Profile.this);
            GridLayoutManager gridLayoutManager = new GridLayoutManager(Profile.this, 3);
            recycler_comments.setLayoutManager(gridLayoutManager);
            recycler_comments.setAdapter(uploadedAdapter);

            recycler_comments.setVisibility(View.VISIBLE);
            findViewById(R.id.txt_comment).setVisibility(View.VISIBLE);
            txt_no_record.setVisibility(View.GONE);
        } else {
            recycler_comments.setVisibility(View.GONE);
            findViewById(R.id.txt_comment).setVisibility(View.GONE);
            txt_no_record.setVisibility(View.VISIBLE);
        }
    }

    /* Follow */

    public void Follow() {
        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.follow(User_Id, TO_Id);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                progressDialog.dismiss();
                if (response.code() == 200) {
                    Profile();
                    Followers();
                    Following();
                }
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
                progressDialog.dismiss();
            }
        });
    }

    // this will hide the bottom mobile navigation controll
    public void Hide_navigation() {

        requestWindowFeature(Window.FEATURE_NO_TITLE);
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        final int flags = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;

        // This work only for android 4.4+
        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {

            getWindow().getDecorView().setSystemUiVisibility(flags);

            // Code below is to handle presses of Volume up or Volume down.
            // Without this, after pressing volume buttons, the navigation bar will
            // show up and won't hide
            final View decorView = getWindow().getDecorView();
            decorView
                    .setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
                        @Override
                        public void onSystemUiVisibilityChange(int visibility) {
                            if ((visibility & View.SYSTEM_UI_FLAG_FULLSCREEN) == 0) {
                                decorView.setSystemUiVisibility(flags);
                            }
                        }
                    });
        }

    }
}
