<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-hls:2.10.3@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/ac599c48a2623867083469f5ecd3489f/jetified-exoplayer-hls-2.10.3/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/ac599c48a2623867083469f5ecd3489f/jetified-exoplayer-hls-2.10.3/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/ac599c48a2623867083469f5ecd3489f/jetified-exoplayer-hls-2.10.3/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/ac599c48a2623867083469f5ecd3489f/jetified-exoplayer-hls-2.10.3/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-hls/2.10.3/2d52620e9307bb3cd48420a082743f03653da5ce/exoplayer-hls-2.10.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-hls/2.10.3/d69ca10f0f02a3815cabd35619d341fd74554912/exoplayer-hls-2.10.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>