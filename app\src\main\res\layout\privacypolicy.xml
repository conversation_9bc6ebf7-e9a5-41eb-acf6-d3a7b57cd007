<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    tools:context=".Activity.Privacypolicy">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:orientation="vertical">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="30dp"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/txt_back"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_back"
                        android:backgroundTint="@color/colorPrimary"
                        android:fontFamily="@font/public_bold"
                        android:gravity="center"
                        android:textColor="@color/colorPrimaryDark"
                        android:textSize="16dp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/toolbar_title"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="10dp"
                    android:fontFamily="@font/public_bold"
                    android:gravity="center"
                    android:text="@string/Privacy_policy"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16dp" />
            </LinearLayout>
        </androidx.appcompat.widget.Toolbar>


    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rl_adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:layout_alignParentBottom="true"
        android:visibility="visible"></RelativeLayout>


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/privacy_policy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="10dp"
                android:fontFamily="@font/public_regular"
                android:paddingTop="15dp"
                android:paddingBottom="15dp"
                android:text=""
                android:textColor="@color/font_dark"
                android:textSize="14sp" />

        </LinearLayout>
    </ScrollView>

</LinearLayout>