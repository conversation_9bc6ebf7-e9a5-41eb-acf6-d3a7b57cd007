<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <ir.alirezabdn.wp7progress.WP10ProgressBar
            android:id="@+id/wp7progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            app:indicatorColor="@color/colorAccent"
            app:indicatorHeight="7"
            app:indicatorRadius="5"
            app:interval="100" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_margin="10dp"
                android:background="@drawable/reward_gra_bg"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txt_avg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/public_bold"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="0"
                    android:textColor="@color/white"
                    android:textSize="30dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:fontFamily="@font/public_medium"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="Rewards Point"
                    android:textColor="@color/white"
                    android:textSize="16dp" />

                <LinearLayout
                    android:id="@+id/ly_reward_claims"
                    android:layout_width="260dp"
                    android:layout_height="40dp"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/round_bor_bg_white"
                    android:gravity="center">

                    <TextView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:background="@drawable/ic_total_rewards"
                        android:gravity="center"
                        android:textSize="14dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:singleLine="true"
                        android:text="Rewards Points Claims"
                        android:textColor="@color/font_dark"
                        android:textSize="12dp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/txt_points"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:fontFamily="@font/public_medium"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="100 Points = 1.0 USD"
                    android:textColor="@color/white"
                    android:textSize="16dp" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_margin="10dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:id="@+id/ly_current_point"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/txt_current_point"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center"
                            android:singleLine="true"
                            android:text="Current Points"
                            android:textColor="@color/colorPrimary"
                            android:textSize="@dimen/text_16" />

                        <TextView
                            android:id="@+id/txt_current_point_line"
                            android:layout_width="50dp"
                            android:layout_height="2dp"
                            android:layout_marginTop="5dp"
                            android:background="@drawable/reward_gra_bg"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ly_withdrawal_point"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginStart="15dp"
                        android:gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/txt_withdrawal_point"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center"
                            android:singleLine="true"
                            android:text="Withdraw History"
                            android:textColor="@color/font_dark"
                            android:textSize="@dimen/text_14" />

                        <TextView
                            android:id="@+id/txt_withdrawal_point_line"
                            android:layout_width="50dp"
                            android:layout_height="2dp"
                            android:layout_marginTop="5dp"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center" />
                    </LinearLayout>

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_points"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="3dp" />


                <LinearLayout
                    android:id="@+id/ly_no_record"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/public_semibold"
                        android:gravity="center"
                        android:text="Whoop!"
                        android:textSize="28dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:text="There are no Record"
                        android:textSize="18dp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>