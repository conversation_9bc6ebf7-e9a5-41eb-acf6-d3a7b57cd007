<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:ignore="MissingConstraints">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="300dp">

            <ImageView
                android:id="@+id/image"
                android:layout_width="match_parent"
                android:layout_height="240dp"
                android:layout_gravity="center"
                android:scaleType="fitXY"/>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="90dp"
                android:layout_alignParentBottom="true"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="15dp"
                android:layout_marginBottom="10dp"
                android:elevation="0dp"
                android:orientation="vertical"
                app:cardCornerRadius="8dp"
                tools:ignore="MissingConstraints">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="10dp"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/public_semibold"
                        android:gravity="center"
                        android:singleLine="true"
                        android:text="Los angeles 2019"
                        android:textColor="@color/font_black"
                        android:textIsSelectable="true"
                        android:textSize="@dimen/text_14" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:weightSum="1.0">

                        <com.iarcuschin.simpleratingbar.SimpleRatingBar
                            android:id="@+id/ratingbar"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:srb_borderColor="@color/rating_fill"
                            app:srb_fillColor="@color/rating_fill"
                            app:srb_numberOfStars="5"
                            app:srb_rating="3"
                            app:srb_starSize="15dp" />

                        <TextView
                            android:id="@+id/txt_avg"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center"
                            android:singleLine="true"
                            android:text="4.5"
                            android:textColor="@color/font_dark"
                            android:textIsSelectable="true"
                            android:textSize="@dimen/text_12" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="7dp"
                        android:weightSum="1.0">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.7">

                            <TextView
                                android:id="@+id/view"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center"
                                android:singleLine="true"
                                android:text="1.25k Views | 200 Comments"
                                android:textColor="@color/font_dark"
                                android:textIsSelectable="true"
                                android:textSize="@dimen/text_12" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.3"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/txt_play"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center"
                                android:singleLine="true"
                                android:text="Play Now"
                                android:textColor="@color/colorPrimary"
                                android:textIsSelectable="true"
                                android:textSize="@dimen/text_12" />

                            <TextView
                                android:id="@+id/txt_play2"
                                android:layout_width="12dp"
                                android:layout_height="12dp"
                                android:layout_marginStart="8dp"
                                android:background="@drawable/ic_play_2"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center"
                                android:singleLine="true"
                                android:textColor="@color/colorPrimary"
                                android:textIsSelectable="true"
                                android:textSize="@dimen/text_14" />
                        </LinearLayout>


                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </RelativeLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>