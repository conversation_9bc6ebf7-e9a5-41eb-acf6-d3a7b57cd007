
buildscript {
    repositories {
        maven { url 'https://plugins.gradle.org/m2/'}
    }
    dependencies {
        classpath 'gradle.plugin.com.onesignal:onesignal-gradle-plugin:[0.11.0, 0.99.99]'
    }
}
apply plugin: 'com.onesignal.androidsdk.onesignal-gradle-plugin'

repositories {
    maven { url 'https://maven.google.com' }
}

apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'

android {
    compileSdkVersion 33

    defaultConfig {
        applicationId "com.videostatus"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        manifestPlaceholders = [
                onesignal_app_id: '************************************',
                onesignal_google_project_number: 'REMOTE'
        ]
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    configurations.all {
        resolutionStrategy.force 'com.github.ybq:Android-SpinKit:1.2.0'
    }

    dexOptions {
        javaMaxHeapSize "4g"
    }

    lintOptions.abortOnError false

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.0.0'
    implementation 'com.google.android.material:material:1.0.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.1.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'

    implementation 'com.squareup.retrofit2:retrofit:2.0.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.0.0'
    implementation 'com.google.code.gson:gson:2.2.4'
    implementation 'com.squareup.okhttp3:logging-interceptor:3.5.0'

    // RecyclerView
    implementation 'androidx.recyclerview:recyclerview:1.0.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'com.squareup.picasso:picasso:2.5.2'

    implementation 'com.google.firebase:firebase-ads:15.0.1'
    implementation 'com.google.firebase:firebase-auth:16.0.1'
    implementation 'com.google.firebase:firebase-messaging:17.0.0'
    implementation 'com.google.android.gms:play-services-auth:17.0.0'

    implementation 'com.github.shadowalker77:wp7progressbar:1.0.5'

    implementation 'com.makeramen:roundedimageview:2.3.0'

    implementation 'com.facebook.android:facebook-share:4.31.0'
    implementation 'com.facebook.android:facebook-messenger:4.31.0'
    implementation 'com.facebook.android:audience-network-sdk:5.+'
    implementation 'com.facebook.android:facebook-android-sdk:4.31.0'

    implementation 'me.relex:circleindicator:1.2.2@aar'

    implementation 'com.onesignal:OneSignal:[3.9.1, 3.99.99]'

    implementation project(path: ':ExoPlayer')
    implementation 'androidx.appcompat:appcompat:1.0.0'

    implementation 'com.github.JakeWharton:ViewPagerIndicator:2.4.1'
    implementation 'com.iarcuschin:simpleratingbar:0.1.5'

    implementation 'de.hdodenhof:circleimageview:3.0.0'
    implementation 'com.google.android.exoplayer:exoplayer:2.8.3'
//    implementation 'com.google.android.exoplayer:exoplayer:r2.5.1'
    /* Optional IMA Extension Dependency If you want to show Video Ads
    in between Your videos Like Youtube */
    implementation 'com.github.danylovolokh:hashtag-helper:1.1.0'
    implementation 'com.wonderkiln:camerakit:0.13.0'
    implementation 'androidx.percentlayout:percentlayout:1.0.0'
    implementation 'com.googlecode.mp4parser:isoparser:1.1.21'
    implementation 'com.github.MasayukiSuda:GPUVideo-android:v0.1.0'
    implementation 'jp.co.cyberagent.android:gpuimage:2.0.3'
    implementation 'com.github.bumptech.glide:glide:4.9.0'
    implementation 'com.mindorks.android:prdownloader:0.6.0'
    implementation 'com.github.ybq:Android-SpinKit:1.2.0'
    implementation 'net.the4thdimension:audio-wife:1.0.3'
    implementation 'com.gmail.samehadar:iosdialog:1.0'

    implementation 'life.knowledge4:k4l-video-trimmer:1.0'
    implementation 'life.knowledge4:k4l-video-trimmer:1.1.3-SNAPSHOT'

}
