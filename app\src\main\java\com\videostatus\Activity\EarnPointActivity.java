package com.videostatus.Activity;

import android.app.Fragment;
import android.app.ProgressDialog;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

import com.facebook.ads.Ad;
import com.facebook.ads.AdError;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.InterstitialAd;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.reward.RewardItem;
import com.google.android.gms.ads.reward.RewardedVideoAd;
import com.google.android.gms.ads.reward.RewardedVideoAdListener;
import com.videostatus.Model.SuccessModel.SuccessModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class EarnPointActivity extends AppCompatActivity implements RewardedVideoAdListener {

    CardView cv_one, cv_two, cv_three;
    ProgressDialog progressDialog;
    PrefManager prefManager;
    private RewardedVideoAd rewardedVideoAd;

    private com.facebook.ads.RewardedVideoAd rewardedVideoAd_fb;

    TextView toolbar_title;
    LinearLayout ly_back;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.earnpoint_activity);

        prefManager = new PrefManager(EarnPointActivity.this);
        progressDialog = new ProgressDialog(EarnPointActivity.this);
        progressDialog.setMessage("Please wait...");
        progressDialog.setCanceledOnTouchOutside(false);

        rewardedVideoAd = MobileAds.getRewardedVideoAdInstance(this);
        rewardedVideoAd.setRewardedVideoAdListener(this);

        rewardedVideoAd_fb = new com.facebook.ads.RewardedVideoAd(EarnPointActivity.this,
                ""+prefManager.getValue("reward_placementid"));

        toolbar_title = findViewById(R.id.toolbar_title);
        toolbar_title.setText("" + getResources().getString(R.string.earn_point));

        ly_back = findViewById(R.id.ly_back);
        ly_back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        cv_one = findViewById(R.id.cv_10);
        cv_two = findViewById(R.id.cv_20);

        cv_one.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                progressDialog.show();

                if (rewardedVideoAd.isLoaded()) {
                    progressDialog.dismiss();
                    rewardedVideoAd.show();

                } else {
                    loadRewardedVideoAd();
                }
            }
        });

        cv_two.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                progressDialog.show();

                if (rewardedVideoAd_fb.isAdLoaded()) {
                    progressDialog.dismiss();
                    rewardedVideoAd_fb.show();

                } else {
                    RewardVideo_fb();
                }
            }
        });

    }

    private void loadRewardedVideoAd() {
        Log.e("id", "" + prefManager.getValue("reward_adid"));
        rewardedVideoAd.loadAd(prefManager.getValue("reward_adid"),
                new AdRequest.Builder().build());
    }

    @Override
    public void onRewardedVideoAdLoaded() {
        if (rewardedVideoAd.isLoaded()) {
            progressDialog.dismiss();
            rewardedVideoAd.show();
        }
    }

    @Override
    public void onRewardedVideoAdOpened() {

    }

    @Override
    public void onRewardedVideoStarted() {

    }

    @Override
    public void onRewardedVideoAdClosed() {
        Add_Earnpoints("6");
    }

    @Override
    public void onRewarded(RewardItem rewardItem) {
    }

    @Override
    public void onRewardedVideoAdLeftApplication() {

    }

    @Override
    public void onRewardedVideoAdFailedToLoad(int i) {
        Toast.makeText(EarnPointActivity.this, "Please try again", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onRewardedVideoCompleted() {
    }

    @Override
    public void onResume() {
        rewardedVideoAd.resume(this);
        super.onResume();
    }

    @Override
    public void onPause() {
        rewardedVideoAd.pause(this);
        super.onPause();
    }

    @Override
    public void onDestroy() {
        rewardedVideoAd.destroy(this);
        super.onDestroy();
    }

    public void RewardVideo_fb() {

        rewardedVideoAd_fb.setAdListener(new com.facebook.ads.RewardedVideoAdListener() {
            @Override
            public void onError(Ad ad, AdError error) {
                // Rewarded video ad failed to load
                Log.e("TAG", "Please Try Again!!!" + error.getErrorMessage());
                progressDialog.dismiss();
            }

            @Override
            public void onAdLoaded(Ad ad) {
                // Rewarded video ad is loaded and ready to be displayed
                Log.e("TAG", "Rewarded video ad is loaded and ready to be displayed!");
                progressDialog.dismiss();
                rewardedVideoAd.show();
            }

            @Override
            public void onAdClicked(Ad ad) {
                // Rewarded video ad clicked
                Log.e("TAG", "Rewarded video ad clicked!");
            }

            @Override
            public void onLoggingImpression(Ad ad) {
                // Rewarded Video ad impression - the event will fire when the
                // video starts playing
                Log.e("TAG", "Rewarded video ad impression logged!");
            }

            @Override
            public void onRewardedVideoCompleted() {
                // Rewarded Video View Complete - the video has been played to the end.
                // You can use this event to initialize your reward
                Log.e("TAG", "Rewarded video completed!");

                // Call method to give reward
                // giveReward();
            }

            @Override
            public void onRewardedVideoClosed() {
                // The Rewarded Video ad was closed - this can occur during the video
                // by closing the app, or closing the end card.
                Log.e("TAG", "Rewarded video ad closed!");
                Add_Earnpoints("6");
            }
        });
        rewardedVideoAd_fb.loadAd();
    }

    private void Add_Earnpoints(String type) {
        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.add_earnpoint(prefManager.getLoginId(),
                type);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                progressDialog.dismiss();
                Log.e("Add Points", "" + response.body().getMessage());
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
                progressDialog.dismiss();
            }
        });
    }
}
