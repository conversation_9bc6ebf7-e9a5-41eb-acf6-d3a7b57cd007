<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_screen3">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:background="@drawable/intro3"
        android:gravity="center_vertical"
        android:orientation="vertical">


        <TextView
            android:layout_width="180dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:fontFamily="@font/public_semibold"
            android:layout_marginEnd="30dp"
            android:gravity="center_vertical"
            android:text="@string/slide_3_title"
            android:textColor="@color/white"
            android:textSize="25dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/public_semibold"
            android:layout_marginTop="20dp"
            android:layout_marginStart="20dp"
            android:textColor="@color/white"
            android:textSize="14dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:fontFamily="@font/public_semibold"
            android:text="@string/slide_1_desc"
            android:layout_marginStart="20dp"
            android:textColor="@android:color/white"
            android:textSize="14dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>