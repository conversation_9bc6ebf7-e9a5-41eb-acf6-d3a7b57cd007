<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">


        <androidx.cardview.widget.CardView
            android:layout_width="180dp"
            android:layout_height="wrap_content"
            android:layout_margin="5dp"
            android:visibility="visible"
            app:cardCornerRadius="5dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="160dp">

                    <ImageView
                        android:id="@+id/iv_thumb"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="fitXY" />

                    <TextView
                        android:id="@+id/txt_duration"
                        android:layout_width="wrap_content"
                        android:layout_height="25dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentRight="true"
                        android:layout_alignParentBottom="true"
                        android:layout_marginEnd="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_marginBottom="10dp"
                        android:background="@drawable/round_bg"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        android:text="04:00"
                        android:textColor="@android:color/white"
                        android:textSize="12dp"
                        android:textStyle="bold" />

                </RelativeLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/margin_50"
                    android:gravity="center_vertical"
                    android:orientation="vertical"
                    android:paddingLeft="@dimen/margin_5"
                    android:paddingRight="@dimen/margin_5">

                    <TextView
                        android:id="@+id/txt_title"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center_vertical"
                        android:singleLine="true"
                        android:textColor="@color/grey_60"
                        android:textSize="14dp"
                        android:textStyle="bold" />


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:textSize="12dp"
                        android:textStyle="bold">

                        <TextView
                            android:id="@+id/txt_cat"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center_vertical"
                            android:textColor="@color/grey_60"
                            android:textSize="12dp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="2"
                            android:gravity="center">

                            <TextView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center_vertical"
                                android:text="@string/icon_eye"
                                android:textColor="@color/grey_60"
                                android:textSize="12dp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/txt_view"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center_vertical"
                                android:textColor="@color/grey_60"
                                android:textSize="12dp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>