<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:ignore="MissingConstraints">

        <androidx.cardview.widget.CardView
            android:id="@+id/cardview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="5dp"
            android:visibility="visible"
            app:cardCornerRadius="8dp"
            app:cardElevation="3dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="100dp">

                    <ImageView
                        android:id="@+id/iv_thumb"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="fitXY" />

                    <TextView
                        android:id="@+id/txt_bookmark"
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/ic_play"
                        android:gravity="center" />

                </RelativeLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>


    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>