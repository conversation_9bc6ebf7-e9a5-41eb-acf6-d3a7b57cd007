<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical"
    tools:context=".Activity.VideoRecord.SoundList">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:gravity="center_vertical">

        <LinearLayout
            android:id="@+id/iv_back"
            android:layout_width="50dp"
            android:gravity="center"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/ic_back"
                android:padding="5dp"
                android:scaleType="fitCenter" />

        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:fontFamily="@font/public_medium"
            android:gravity="center_vertical"
            android:text="SoundList"
            android:textColor="@color/black"
            android:textSize="@dimen/text_16" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/listview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/margin_5"
            android:descendantFocusability="blocksDescendants"
            android:divider="@color/transparent"
            android:dividerHeight="0dp">

        </androidx.recyclerview.widget.RecyclerView>

        <ProgressBar
            android:id="@+id/pbar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />

    </RelativeLayout>
</LinearLayout>
