<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="200dp"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="10dp">


    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toRightOf="@+id/pbar"
        android:gravity="center"
        android:text="Please Wait"
        android:textColor="@color/black"
        android:textSize="20dp" />

    <ProgressBar
        android:id="@+id/pbar"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginTop="20dp"
        android:indeterminate="false"
        android:progress="0"
        android:theme="@style/Progressbar_style" />

</LinearLayout>