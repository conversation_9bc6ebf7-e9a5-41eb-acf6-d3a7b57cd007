<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/nav_header_height"
    android:background="@color/font_dark"
    android:gravity="center_vertical"
    android:orientation="vertical"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/iv_user_thumb"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:scaleType="fitXY"
            android:background="@mipmap/ic_launcher"
            android:elevation="5dp"
            app:riv_border_color="@color/white"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="15dp"
            app:riv_border_width="2dip"
            app:riv_mutate_background="true"
            app:riv_oval="true"
            app:riv_tile_mode="clamp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="10dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txt_username"
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:fontFamily="@font/public_regular"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/nav_header_vertical_spacing"
                android:textColor="@android:color/white"
                android:textSize="@dimen/text_16" />

            <TextView
                android:id="@+id/txt_email"
                android:layout_width="match_parent"
                android:layout_height="25dp"
                android:fontFamily="@font/public_medium"
                android:gravity="center_vertical"
                android:layout_marginBottom="10dp"
                android:textColor="@android:color/white"
                android:textSize="12dp" />
        </LinearLayout>

    </RelativeLayout>
</LinearLayout>
