<component name="libraryTable">
  <library name="Gradle: me.relex:circleindicator:1.2.2@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/fa322bc5c11406a81ade705909ce88b7/jetified-circleindicator-1.2.2/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/fa322bc5c11406a81ade705909ce88b7/jetified-circleindicator-1.2.2/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/fa322bc5c11406a81ade705909ce88b7/jetified-circleindicator-1.2.2/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/me.relex/circleindicator/1.2.2/****************************************/circleindicator-1.2.2-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/me.relex/circleindicator/1.2.2/739ce17f5368373ddbce9d5ec0fd3aa82eecf4b/circleindicator-1.2.2-sources.jar!/" />
    </SOURCES>
  </library>
</component>