<resources xmlns:tools="http://schemas.android.com/tools">

    <dimen name="design_bottom_navigation_text_size" tools:override="true">10sp</dimen>
    <dimen name="design_bottom_navigation_active_text_size" tools:override="true">12sp</dimen>

    <dimen name="margin_3">3dp</dimen>
    <dimen name="margin_5">5dp</dimen>
    <dimen name="margin_10">10dp</dimen>
    <dimen name="margin_15">15dp</dimen>
    <dimen name="margin_20">20dp</dimen>
    <dimen name="margin_50">50dp</dimen>
    <dimen name="margin_60">60dp</dimen>

    <dimen name="spacing_xsmall">2dp</dimen>
    <dimen name="spacing_small">3dp</dimen>
    <dimen name="spacing_medium">5dp</dimen>
    <dimen name="spacing_xmedium">7dp</dimen>
    <dimen name="spacing_middle">10dp</dimen>
    <dimen name="spacing_large">15dp</dimen>
    <dimen name="spacing_smlarge">18dp</dimen>
    <dimen name="spacing_mlarge">20dp</dimen>
    <dimen name="spacing_mxlarge">25dp</dimen>
    <dimen name="spacing_xlarge">35dp</dimen>
    <dimen name="spacing_xmlarge">40dp</dimen>
    <dimen name="spacing_xxlarge">50dp</dimen>
    <dimen name="spacing_xxxlarge">55dp</dimen>
    <dimen name="appbar_padding_top">8dp</dimen>

    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="fab_margin">36dp</dimen>
    <dimen name="dots_height">40dp</dimen>
    <dimen name="dots_margin_bottom">20dp</dimen>
    <dimen name="img_width_height">220dp</dimen>

    <dimen name="slide_title">20dp</dimen>
    <dimen name="slide_desc">16dp</dimen>

    <dimen name="desc_padding">40dp</dimen>
    <dimen name="logo_w_h">100dp</dimen>

    <dimen name="icon">35dp</dimen>
    <dimen name="icon_ring">33dp</dimen>


    <dimen name="five">5dp</dimen>
    <dimen name="ten">10dp</dimen>
    <dimen name="fifteen">15dp</dimen>


    <dimen name="min_height">150dp</dimen>

    <dimen name="editor_size">35dp</dimen>
    <dimen name="normal_margin">8dp</dimen>
    <dimen name="large_margin">16dp</dimen>

    <dimen name="text_18">18dp</dimen>
    <dimen name="text_16">16dp</dimen>
    <dimen name="text_14">14dp</dimen>
    <dimen name="text_12">12dp</dimen>
    <dimen name="text_10">10dp</dimen>
</resources>
