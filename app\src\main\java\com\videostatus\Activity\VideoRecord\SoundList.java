package com.videostatus.Activity.VideoRecord;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import com.downloader.Error;
import com.downloader.OnCancelListener;
import com.downloader.OnDownloadListener;
import com.downloader.OnPauseListener;
import com.downloader.OnProgressListener;
import com.downloader.OnStartOrResumeListener;
import com.downloader.PRDownloader;
import com.downloader.Progress;
import com.downloader.request.DownloadRequest;

import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.github.ybq.android.spinkit.SpinKitView;
import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.android.exoplayer2.ExoPlayerFactory;
import com.google.android.exoplayer2.PlaybackParameters;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.Timeline;
import com.google.android.exoplayer2.source.ExtractorMediaSource;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.TrackGroupArray;
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
import com.google.android.exoplayer2.trackselection.TrackSelectionArray;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.google.android.exoplayer2.util.Util;
import com.videostatus.Adapter.SoundAdapter;
import com.videostatus.Model.SoundModel.Result;
import com.videostatus.Model.SoundModel.SoundModel;
import com.videostatus.R;
import com.videostatus.Utility.Constant;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SoundList extends AppCompatActivity implements Player.EventListener {

    RecyclerView listview;
    SoundAdapter soundAdapter;
    List<Result> Soundlist;
    LinearLayout iv_back;
    DownloadRequest prDownloader;
    static boolean active = false;

    Context context;

    ProgressBar pbar;

    public static String running_sound_id;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.soundlist);
        context = getApplicationContext();

        running_sound_id = "none";

        pbar = findViewById(R.id.pbar);
        iv_back = findViewById(R.id.iv_back);
        iv_back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        listview = findViewById(R.id.listview);
        listview.setLayoutManager(new LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false));
        listview.setNestedScrollingEnabled(false);
        listview.setHasFixedSize(true);
        listview.getLayoutManager().setMeasurementCacheEnabled(false);

        PRDownloader.initialize(context);

        GetSound();
    }

    private void GetSound() {
        pbar.setVisibility(View.VISIBLE);
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SoundModel> call = bookNPlayAPI.get_sound();
        call.enqueue(new Callback<SoundModel>() {
            @Override
            public void onResponse(Call<SoundModel> call, Response<SoundModel> response) {
                pbar.setVisibility(View.GONE);
                if (response.code() == 200) {
                    Log.e("GetSound", "" + response);
                    Soundlist = new ArrayList<>();
                    Soundlist = response.body().getResult();
                    Log.e("SoundList", "" + Soundlist.size());

                    soundAdapter = new SoundAdapter(SoundList.this, Soundlist, new SoundAdapter.OnItemClickListener() {
                        @Override
                        public void onItemClick(View view, int postion, Result item) {
                            Log.e("view", "" + view.getId());
                            previous_view = view;

                            if (view.getId() == R.id.iv_done) {
                                StopPlaying();
                                Down_load_mp3(item.getId(), item.getTitle(), item.getUrl());
                            } else {
                                if (thread != null && !thread.isAlive()) {
                                    StopPlaying();
                                    playaudio(view, item);
                                } else if (thread == null) {
                                    StopPlaying();
                                    playaudio(view, item);
                                }
                            }
                        }
                    });
                    listview.setAdapter(soundAdapter);
                }
            }

            @Override
            public void onFailure(Call<SoundModel> call, Throwable t) {
                pbar.setVisibility(View.GONE);
            }
        });
    }

    View previous_view;
    Thread thread;
    SimpleExoPlayer player;
    String previous_url = "none";

    public void playaudio(View view, final Result item) {


        if (previous_url.equals(item.getUrl())) {

            previous_url = "none";
            running_sound_id = "none";
        } else {

            previous_url = item.getUrl();
            running_sound_id = item.getId();

            DefaultTrackSelector trackSelector = new DefaultTrackSelector();
            player = ExoPlayerFactory.newSimpleInstance(context, trackSelector);

            DataSource.Factory dataSourceFactory = new DefaultDataSourceFactory(context,
                    Util.getUserAgent(context, "VideoStatus"));

            MediaSource videoSource = new ExtractorMediaSource.Factory(dataSourceFactory)
                    .createMediaSource(Uri.parse(item.getUrl()));

            player.prepare(videoSource);
            player.addListener(this);

            player.setPlayWhenReady(true);

        }

    }

    public void StopPlaying() {
        if (player != null) {
            player.setPlayWhenReady(false);
            player.removeListener(this);
            player.release();
        }
        show_Stop_state();
    }

    @Override
    public void onStart() {
        super.onStart();
        active = true;
    }

    @Override
    public void onStop() {
        super.onStop();
        active = false;

        running_sound_id = "null";

        if (player != null) {
            player.setPlayWhenReady(false);
            player.removeListener(this);
            player.release();
        }

        show_Stop_state();

    }

    public void Show_Run_State() {

        if (previous_view != null) {
//            ((SpinKitView) previous_view.findViewById(R.id.sp_loading)).setVisibility(View.GONE);
//            ((TextView) previous_view.findViewById(R.id.txt_pause)).setVisibility(View.VISIBLE);
//            ((ImageView) previous_view.findViewById(R.id.iv_done)).setVisibility(View.VISIBLE);
        }
    }

    public void Show_loading_state() {
//        previous_view.findViewById(R.id.txt_play).setVisibility(View.GONE);
//        previous_view.findViewById(R.id.sp_loading).setVisibility(View.VISIBLE);
    }

    public void show_Stop_state() {
        if (previous_view != null) {
//            previous_view.findViewById(R.id.txt_play).setVisibility(View.VISIBLE);
//            previous_view.findViewById(R.id.sp_loading).setVisibility(View.GONE);
//            previous_view.findViewById(R.id.txt_pause).setVisibility(View.GONE);
//            previous_view.findViewById(R.id.iv_done).setVisibility(View.GONE);
        }
        running_sound_id = "none";
    }

    public void Down_load_mp3(final String id, final String sound_name, String url) {

        pbar.setVisibility(View.VISIBLE);

        prDownloader = PRDownloader.download(url, Constant.app_folder, Constant.SelectedAudio_AAC)
                .build()
                .setOnStartOrResumeListener(new OnStartOrResumeListener() {
                    @Override
                    public void onStartOrResume() {

                    }
                })
                .setOnPauseListener(new OnPauseListener() {
                    @Override
                    public void onPause() {

                    }
                })
                .setOnCancelListener(new OnCancelListener() {
                    @Override
                    public void onCancel() {

                    }
                })
                .setOnProgressListener(new OnProgressListener() {
                    @Override
                    public void onProgress(Progress progress) {

                    }
                });

        prDownloader.start(new OnDownloadListener() {
            @Override
            public void onDownloadComplete() {
                pbar.setVisibility(View.GONE);
                Intent output = new Intent();
                output.putExtra("isSelected", "yes");
                output.putExtra("sound_name", sound_name);
                output.putExtra("sound_id", id);
                SoundList.this.setResult(RESULT_OK, output);
                SoundList.this.finish();
                SoundList.this.overridePendingTransition(R.anim.in_from_top, R.anim.out_from_bottom);
            }

            @Override
            public void onError(Error error) {
                pbar.setVisibility(View.GONE);
            }
        });

    }

    @Override
    public void onTimelineChanged(Timeline timeline, @Nullable Object manifest, int reason) {

    }

    @Override
    public void onTracksChanged(TrackGroupArray trackGroups, TrackSelectionArray trackSelections) {

    }

    @Override
    public void onLoadingChanged(boolean isLoading) {

    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {

        if (playbackState == Player.STATE_BUFFERING) {
            Show_loading_state();
        } else if (playbackState == Player.STATE_READY) {
            Show_Run_State();
        } else if (playbackState == Player.STATE_ENDED) {
            show_Stop_state();
        }

    }

    @Override
    public void onRepeatModeChanged(int repeatMode) {

    }

    @Override
    public void onShuffleModeEnabledChanged(boolean shuffleModeEnabled) {

    }

    @Override
    public void onPlayerError(ExoPlaybackException error) {

    }

    @Override
    public void onPositionDiscontinuity(int reason) {

    }

    @Override
    public void onPlaybackParametersChanged(PlaybackParameters playbackParameters) {

    }

    @Override
    public void onSeekProcessed() {

    }

}
