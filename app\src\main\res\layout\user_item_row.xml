<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ly_main"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="visible"
        tools:ignore="MissingConstraints">

        <androidx.cardview.widget.CardView
            android:id="@+id/cardview"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_margin="10dp"
            android:orientation="vertical"
            app:cardCornerRadius="8dp"
            tools:ignore="MissingConstraints">

            <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/iv_thumb"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:adjustViewBounds="true"
                        android:scaleType="fitXY"
                        android:visibility="visible"
                        app:riv_border_color="@color/white"
                        app:riv_border_width="1dip"
                        app:riv_corner_radius="60dip"
                        app:riv_oval="false"
                        app:riv_tile_mode="clamp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:layout_marginTop="5dp"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:singleLine="true"
                        android:text="Chirag Patel"
                        android:textColor="@color/colorPrimary"
                        android:textSize="14dp" />

                    <TextView
                        android:id="@+id/txt_mobile"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:layout_marginTop="5dp"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:singleLine="true"
                        android:text="135 PTS"
                        android:textColor="@color/font_dark"
                        android:textSize="12dp" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>