package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.videostatus.Activity.StatusDetails;
import com.videostatus.Model.VideoModel.Result;
import com.videostatus.R;
import com.videostatus.Interface.ShareAll;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class Latest_ViewAll_Adapter extends RecyclerView.Adapter<Latest_ViewAll_Adapter.MyViewHolder> {

    private List<Result> CategoryList;
    Context mcontext;

    ShareAll shareAll;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        TextView txt_duration, txt_title, txt_cat, txt_view, txt_download;
        ImageView iv_thumb;
        CardView card_view;

        public MyViewHolder(View view) {
            super(view);
            txt_duration = (TextView) view.findViewById(R.id.txt_duration);
            iv_thumb = (ImageView) view.findViewById(R.id.iv_thumb);
            txt_title = (TextView) view.findViewById(R.id.txt_title);
            txt_cat = (TextView) view.findViewById(R.id.txt_cat);
            txt_view = (TextView) view.findViewById(R.id.txt_view);
            txt_download = (TextView) view.findViewById(R.id.txt_download);
            card_view = (CardView) view.findViewById(R.id.card_view);
        }
    }

    public Latest_ViewAll_Adapter(Context context, List<Result> moviesList, ShareAll shareAll) {
        this.CategoryList = moviesList;
        this.mcontext = context;
        this.shareAll = shareAll;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.latest_viewall_item_row, parent, false);

        return new MyViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        holder.txt_duration.setText(CategoryList.get(position).getVideoDuration());
        holder.txt_cat.setText(CategoryList.get(position).getCategoryName());
        holder.txt_view.setText(CategoryList.get(position).getView());
        holder.txt_download.setText(CategoryList.get(position).getDowanload());

//        Picasso.with(mcontext).load(CategoryList.get(position).getThumbnailImg()).resize(400, 400)
//                .placeholder(R.drawable.ic_launcher).centerInside().into(holder.iv_thumb);

        holder.txt_title.setText(CategoryList.get(position).getVideoTitle());
        try {
            //Dates to compare
            String CurrentDate = "2018-12-29 11:38:22";
            String FinalDate = CategoryList.get(position).getCDate();
            Date date1;
            Date date2;
            SimpleDateFormat dates = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //Setting dates
            date1 = dates.parse(CurrentDate);
            date2 = dates.parse(FinalDate);
            //Comparing dates
            long difference = Math.abs(date1.getTime() - date2.getTime());
            long differenceDates = difference / (24 * 60 * 60 * 1000);
            //Convert long to String
            String dayDifference = Long.toString(differenceDates);
            Log.e("HERE", "HERE: " + dayDifference);
        } catch (Exception exception) {
            Log.e("DIDN'T WORK", "exception " + exception);
        }

        holder.card_view.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(mcontext, StatusDetails.class);
                intent.putExtra("id", "" + CategoryList.get(position).getId());
                intent.putExtra("cat_id", "" + CategoryList.get(position).getCatId());
                intent.putExtra("user_id", "" + CategoryList.get(position).getUserId());
                intent.putExtra("video_title", "" + CategoryList.get(position).getVideoTitle());
                intent.putExtra("video_type", "" + CategoryList.get(position).getView());
                intent.putExtra("video_duration", "" + CategoryList.get(position).getVideoDuration());
                intent.putExtra("video_url", "" + CategoryList.get(position).getVideoUrl());
                intent.putExtra("thumbnail_img", "" + CategoryList.get(position).getThumbnailImg());
                intent.putExtra("video_descripation", "" + CategoryList.get(position).getVideoDescripation());
                intent.putExtra("dowanload", "" + CategoryList.get(position).getDowanload());
                intent.putExtra("view", "" + CategoryList.get(position).getView());
                intent.putExtra("status", "" + CategoryList.get(position).getStatus());
                intent.putExtra("category_name", "" + CategoryList.get(position).getCategoryName());
                mcontext.startActivity(intent);
            }
        });
    }

    @Override
    public int getItemCount() {
        return CategoryList.size();
    }

}
