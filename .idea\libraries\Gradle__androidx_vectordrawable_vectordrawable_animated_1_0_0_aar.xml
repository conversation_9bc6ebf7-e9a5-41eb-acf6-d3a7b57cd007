<component name="libraryTable">
  <library name="Gradle: androidx.vectordrawable:vectordrawable-animated:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/f6d769a3b08983b672c26267e08574a7/vectordrawable-animated-1.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/f6d769a3b08983b672c26267e08574a7/vectordrawable-animated-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/f6d769a3b08983b672c26267e08574a7/vectordrawable-animated-1.0.0/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.vectordrawable/vectordrawable-animated/1.0.0/24f92bcc89d979cd8b99ae40def4e395850a6466/vectordrawable-animated-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>