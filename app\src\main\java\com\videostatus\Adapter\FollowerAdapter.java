package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.makeramen.roundedimageview.RoundedImageView;
import com.squareup.picasso.Picasso;
import com.videostatus.Activity.Profile;
import com.videostatus.Model.FollowModel.Result;
import com.videostatus.R;
import com.videostatus.Interface.ShareAll;

import java.util.List;

public class FollowerAdapter extends RecyclerView.Adapter<FollowerAdapter.MyViewHolder> {

    private List<Result> UplaodedList;
    Context mcontext;

    ShareAll shareAll;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        TextView txt_name, txt_points;
        RoundedImageView iv_user_pic;
        CardView cardview;

        public MyViewHolder(View view) {
            super(view);
            txt_name = view.findViewById(R.id.txt_name);
            iv_user_pic = view.findViewById(R.id.iv_user_pic);
            txt_points = view.findViewById(R.id.txt_points);
            cardview = view.findViewById(R.id.cardview);
        }
    }

    public FollowerAdapter(Context context, List<Result> UplaodedList, ShareAll shareAll) {
        this.UplaodedList = UplaodedList;
        this.mcontext = context;
        this.shareAll = shareAll;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.follower_item_row, parent, false);

        return new MyViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        holder.txt_name.setText(UplaodedList.get(position).getFullname());

        holder.txt_points.setText(UplaodedList.get(position).getTotalPoints() + " PT");

        Picasso.with(mcontext).load(UplaodedList.get(position).getProfileImg()).resize(400, 400)
                .placeholder(R.drawable.no_user).centerInside().into(holder.iv_user_pic);

        holder.cardview.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(mcontext, Profile.class);
                intent.putExtra("Id", "" + UplaodedList.get(position).getId());
                intent.putExtra("TO_Id", "" + UplaodedList.get(position).getId());
                mcontext.startActivity(intent);
            }
        });
    }

    @Override
    public int getItemCount() {
        return UplaodedList.size();
    }

    public String withSuffix(long count) {
        if (count < 1000) return "" + count;
        int exp = (int) (Math.log(count) / Math.log(1000));
        return String.format("%.1f %c",
                count / Math.pow(1000, exp),
                "kMGTPE".charAt(exp - 1));
    }
}
