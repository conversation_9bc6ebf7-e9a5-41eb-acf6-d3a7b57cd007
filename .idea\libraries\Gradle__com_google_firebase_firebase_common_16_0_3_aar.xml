<component name="libraryTable">
  <library name="Gradle: com.google.firebase:firebase-common:16.0.3@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/fd74594a444c436b9f151569c6efd069/jetified-firebase-common-16.0.3/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/fd74594a444c436b9f151569c6efd069/jetified-firebase-common-16.0.3/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/fd74594a444c436b9f151569c6efd069/jetified-firebase-common-16.0.3/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.firebase/firebase-common/16.0.3/d8fb501ad05b51e2a89e23dafeb391fd62a030b5/firebase-common-16.0.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES />
  </library>
</component>