<component name="libraryTable">
  <library name="Gradle: life.knowledge4:k4l-video-trimmer:1.1.3-SNAPSHOT@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/49341f614c642c942f4a91c85d1b7331/jetified-k4l-video-trimmer-1.1.3-SNAPSHOT/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/49341f614c642c942f4a91c85d1b7331/jetified-k4l-video-trimmer-1.1.3-SNAPSHOT/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/49341f614c642c942f4a91c85d1b7331/jetified-k4l-video-trimmer-1.1.3-SNAPSHOT/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/life.knowledge4/k4l-video-trimmer/1.1.3-SNAPSHOT/7cd60ad25e63b6d8dce30d519fb15dd9a287701a/k4l-video-trimmer-1.1.3-SNAPSHOT-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/life.knowledge4/k4l-video-trimmer/1.1.3-SNAPSHOT/5ea291aaf20a0ea30d9d9b27deea71d817ef1da7/k4l-video-trimmer-1.1.3-SNAPSHOT-sources.jar!/" />
    </SOURCES>
  </library>
</component>