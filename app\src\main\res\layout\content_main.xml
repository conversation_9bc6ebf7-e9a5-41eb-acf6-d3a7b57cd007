<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ir.alirezabdn.wp7progress.WP10ProgressBar
        android:id="@+id/wp7progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        app:indicatorColor="@color/colorPrimary"
        app:indicatorHeight="7"
        app:indicatorRadius="5"
        app:interval="100" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:weightSum="1.0">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <FrameLayout
                android:id="@+id/rootLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

            </FrameLayout>

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/ly_bottom"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_alignParentBottom="true"
            android:background="@color/transparent"
            android:visibility="visible">

            <com.google.android.material.bottomnavigation.BottomNavigationView
                android:id="@+id/navigation"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:background="@drawable/bottom_gra_bg"
                app:itemIconTint="@color/color_selector"
                app:itemTextColor="@color/color_selector"
                app:labelVisibilityMode="labeled"
                app:menu="@menu/navigation" />
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>