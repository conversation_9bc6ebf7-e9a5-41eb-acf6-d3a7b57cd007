package com.videostatus.Activity;

import android.app.ProgressDialog;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.videostatus.Adapter.CategoryVideoAdapter;
import com.videostatus.Model.VideoModel.VideoModel;
import com.videostatus.R;
import com.videostatus.Interface.ShareAll;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.util.ArrayList;
import java.util.List;

import ir.alirezabdn.wp7progress.WP10ProgressBar;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class VideoList extends AppCompatActivity implements ShareAll {

    public VideoList() {
    }

    WP10ProgressBar progressBar;
    RecyclerView recycler_video;
    LinearLayout ly_back;
    TextView toolbar_title;

    ProgressDialog progressDialog;
    List<com.videostatus.Model.VideoModel.Result> CategoryVideoList;
    String Cat_Id;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.videolist);

        progressDialog = new ProgressDialog(VideoList.this);
        progressDialog.setMessage("Please wait...");
        progressDialog.setCanceledOnTouchOutside(false);

        Toolbar toolbar = (Toolbar) findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        progressBar = findViewById(R.id.wp7progressBar);
        toolbar_title=findViewById(R.id.toolbar_title);

        ly_back=findViewById(R.id.ly_back);
        ly_back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        recycler_video = (RecyclerView) findViewById(R.id.recycler_video);

        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            Cat_Id = bundle.getString("Id");
            Log.e("Cat_Id", "" + Cat_Id);

            toolbar_title.setText(""+bundle.getString("Name"));

            CategoryVideoList();
        }

    }

    private void CategoryVideoList() {

        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<VideoModel> call = bookNPlayAPI.related_video(Cat_Id);
        call.enqueue(new Callback<VideoModel>() {
            @Override
            public void onResponse(Call<VideoModel> call, Response<VideoModel> response) {
                progressDialog.hide();
                if (response.code() == 200) {
                    CategoryVideoList = new ArrayList<>();
                    CategoryVideoList = response.body().getResult();
                    Log.e("CategoryVideoList", "" + CategoryVideoList.size());

                    CategoryVideoAdapter categoryVideoAdapter = new CategoryVideoAdapter(VideoList.this,
                            CategoryVideoList, "");
                    GridLayoutManager gridLayoutManager = new GridLayoutManager(getApplicationContext(), 2);
                    recycler_video.setLayoutManager(gridLayoutManager);
                    recycler_video.setItemAnimator(new DefaultItemAnimator());
                    recycler_video.setAdapter(categoryVideoAdapter);
                    categoryVideoAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<VideoModel> call, Throwable t) {
                progressDialog.hide();
            }
        });
    }

    @Override
    public void WhatsappShare() {

    }
}
