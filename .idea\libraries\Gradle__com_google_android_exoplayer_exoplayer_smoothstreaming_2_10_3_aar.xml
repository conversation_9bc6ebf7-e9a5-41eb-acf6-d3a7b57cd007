<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer-smoothstreaming:2.10.3@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/a5abd66199e07af16ac878a4f7aa2cd2/jetified-exoplayer-smoothstreaming-2.10.3/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/a5abd66199e07af16ac878a4f7aa2cd2/jetified-exoplayer-smoothstreaming-2.10.3/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/a5abd66199e07af16ac878a4f7aa2cd2/jetified-exoplayer-smoothstreaming-2.10.3/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-smoothstreaming/2.10.3/b17adf40efa51b1335b87c18f709ffc94d8a039/exoplayer-smoothstreaming-2.10.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer-smoothstreaming/2.10.3/51994b7a05229986da37cd27f2887197ab0de3bc/exoplayer-smoothstreaming-2.10.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>