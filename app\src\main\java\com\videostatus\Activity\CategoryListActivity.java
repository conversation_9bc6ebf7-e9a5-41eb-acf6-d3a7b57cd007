package com.videostatus.Activity;

import android.app.Fragment;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.videostatus.Adapter.CategoryAdapter;
import com.videostatus.Adapter.CategoryAdapter2;
import com.videostatus.Model.CategoryModel.CategoryModel;
import com.videostatus.Model.CategoryModel.Result;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.util.ArrayList;
import java.util.List;

import ir.alirezabdn.wp7progress.WP10ProgressBar;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CategoryListActivity extends AppCompatActivity {

    public CategoryListActivity() {
    }

    WP10ProgressBar progressBar;
    RecyclerView recycler_category;
    CategoryAdapter categoryAdapter;
    private SwipeRefreshLayout swipeContainer;
    List<Result> CategoryList;

    PrefManager prefManager;
    Toolbar toolbar;
    LinearLayout ly_back;
    TextView toolbar_title;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.category_activity);

        PrefManager.forceRTLIfSupported(getWindow(), CategoryListActivity.this);
        prefManager = new PrefManager(CategoryListActivity.this);

        toolbar = findViewById(R.id.toolbar);

        toolbar_title = findViewById(R.id.toolbar_title);
        toolbar_title.setText(getResources().getString(R.string.category));

        progressBar = findViewById(R.id.wp7progressBar);
        recycler_category = findViewById(R.id.recycler_category);

        ly_back = findViewById(R.id.ly_back);
        ly_back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        swipeContainer = findViewById(R.id.swipeContainer);
        swipeContainer.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                swipeContainer.setRefreshing(true);
                getCategory();
            }
        });
        swipeContainer.setColorSchemeResources(android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light);

        getCategory();

    }

    private void getCategory() {

        progressBar.showProgressBar();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<CategoryModel> call = bookNPlayAPI.Category();
        call.enqueue(new Callback<CategoryModel>() {
            @Override
            public void onResponse(Call<CategoryModel> call, Response<CategoryModel> response) {
                progressBar.hideProgressBar();
                swipeContainer.setRefreshing(false);
                if (response.code() == 200) {
                    CategoryList = new ArrayList<>();
                    CategoryList = response.body().getResult();
                    Log.e("CategoryList", "" + CategoryList.size());

                    categoryAdapter = new CategoryAdapter(CategoryListActivity.this, CategoryList,"");
                    GridLayoutManager manager = new GridLayoutManager(CategoryListActivity.this, 2, GridLayoutManager.VERTICAL, false);
                    recycler_category.setLayoutManager(manager);
                    recycler_category.setItemAnimator(new DefaultItemAnimator());
                    recycler_category.setAdapter(categoryAdapter);
                    categoryAdapter.notifyDataSetChanged();

                }
            }

            @Override
            public void onFailure(Call<CategoryModel> call, Throwable t) {
                progressBar.hideProgressBar();
            }
        });
    }
}
