package com.videostatus.Activity.VideoRecord;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.media.MediaMetadataRetriever;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.coremedia.iso.boxes.Container;
import com.coremedia.iso.boxes.MovieHeaderBox;
import com.daasuu.gpuv.composer.GPUMp4Composer;
import com.googlecode.mp4parser.FileDataSourceImpl;
import com.googlecode.mp4parser.authoring.Movie;
import com.googlecode.mp4parser.authoring.Track;
import com.googlecode.mp4parser.authoring.builder.DefaultMp4Builder;
import com.googlecode.mp4parser.authoring.container.mp4.MovieCreator;
import com.googlecode.mp4parser.authoring.tracks.AppendTrack;
import com.googlecode.mp4parser.authoring.tracks.CroppedTrack;
import com.googlecode.mp4parser.util.Matrix;
import com.googlecode.mp4parser.util.Path;
import com.videostatus.R;
import com.videostatus.SegmentProgress.ProgressBarListener;
import com.videostatus.SegmentProgress.SegmentedProgressBar;
import com.videostatus.Utility.Constant;
import com.videostatus.Utility.FileUtils;
import com.videostatus.Utility.MergeVideoAudio;
import com.videostatus.Utility.Utils;
import com.wonderkiln.camerakit.CameraKit;
import com.wonderkiln.camerakit.CameraKitError;
import com.wonderkiln.camerakit.CameraKitEvent;
import com.wonderkiln.camerakit.CameraKitEventListener;
import com.wonderkiln.camerakit.CameraKitImage;
import com.wonderkiln.camerakit.CameraKitVideo;
import com.wonderkiln.camerakit.CameraView;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.channels.WritableByteChannel;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class VideoRecord extends AppCompatActivity implements View.OnClickListener {

    CameraView cameraView;
    ImageView iv_record;
    LinearLayout ly_upload, camera_options;
    ImageView iv_done, iv_back, iv_rotate_camera, iv_flash_camera;
    TextView txt_add_sound;
    boolean is_recording = false;

    SegmentedProgressBar video_progress;

    ArrayList<String> videopaths = new ArrayList<>();

    long time_in_milis = 0;
    int sec_passed = 0;
    int number = 0;
    boolean is_flash_on = false;

    boolean is_recording_timer_enable;
    int recording_time = 3;

    public static int Sounds_list_Request_code = 1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Hide_navigation();
        setContentView(R.layout.activity_video_recoder);

        Constant.Selected_sound_id = "null";
        Constant.recording_duration = Constant.max_recording_duration;

        Init();

        cameraView.addCameraKitListener(new CameraKitEventListener() {
            @Override
            public void onEvent(CameraKitEvent cameraKitEvent) {
            }

            @Override
            public void onError(CameraKitError cameraKitError) {
            }

            @Override
            public void onImage(CameraKitImage cameraKitImage) {
            }

            @Override
            public void onVideo(CameraKitVideo cameraKitVideo) {

            }
        });

        Intent intent = getIntent();
        if (intent.hasExtra("sound_name")) {
            txt_add_sound.setText(intent.getStringExtra("sound_name"));
            Constant.Selected_sound_id = intent.getStringExtra("sound_id");
            PreparedAudio();
        }

        // this is code hold to record the video
        final Timer[] timer = {new Timer()};
        iv_record.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    Log.e("Time", "Action Down");
                    timer[0] = new Timer();
                    timer[0].schedule(new TimerTask() {
                        @Override
                        public void run() {

                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    if (!is_recording)
                                        Start_or_Stop_Recording();
                                }
                            });
                        }
                    }, 200);

                } else if (event.getAction() == MotionEvent.ACTION_UP) {
                    timer[0].cancel();
                    Log.e("Time", "Action UP");
                    if (is_recording) {
                        Start_or_Stop_Recording();
                    }
                }
                return false;
            }
        });

        initlize_Video_progress();

    }

    public void Init() {
        iv_record = findViewById(R.id.iv_record);
        cameraView = findViewById(R.id.cameraView);
        video_progress = findViewById(R.id.video_progress);
        ly_upload = findViewById(R.id.ly_upload);
        camera_options = findViewById(R.id.camera_options);

        iv_done = findViewById(R.id.iv_done);
        iv_done.setEnabled(false);

        iv_back = findViewById(R.id.iv_back);
        iv_rotate_camera = findViewById(R.id.iv_rotate_camera);
        iv_record = findViewById(R.id.iv_record);
        iv_flash_camera = findViewById(R.id.iv_flash_camera);
        txt_add_sound = findViewById(R.id.txt_add_sound);

        iv_done.setOnClickListener(this);
        iv_back.setOnClickListener(this);
        iv_rotate_camera.setOnClickListener(this);
        iv_record.setOnClickListener(this);
        iv_flash_camera.setOnClickListener(this);
        txt_add_sound.setOnClickListener(this);
        ly_upload.setOnClickListener(this);

    }

    @SuppressLint("WrongConstant")
    @Override
    public void onClick(View v) {

        switch (v.getId()) {
            case R.id.iv_rotate_camera:
                RotateCamera();
                break;

            case R.id.ly_upload:
                Pick_video_from_gallery();
                break;

            case R.id.iv_done:
                append();
                break;

            case R.id.iv_record:
                Start_or_Stop_Recording();
                break;
            case R.id.iv_flash_camera:
                if (is_flash_on) {
                    is_flash_on = false;
                    cameraView.setFlash(0);
                    iv_flash_camera.setImageDrawable(getResources().getDrawable(R.drawable.ic_flash_on));
                } else {
                    is_flash_on = true;
                    cameraView.setFlash(CameraKit.Constants.FLASH_TORCH);
                    iv_flash_camera.setImageDrawable(getResources().getDrawable(R.drawable.ic_flash_off));
                }
                break;

            case R.id.iv_back:
                onBackPressed();
                break;

            case R.id.txt_add_sound:
                Intent intent = new Intent(this, SoundList.class);
                startActivityForResult(intent, Sounds_list_Request_code);
                overridePendingTransition(R.anim.in_from_bottom, R.anim.out_to_top);
                break;
        }

    }

    // this will play the sound with the video when we select the audio
    MediaPlayer audio;

    public void PreparedAudio() {
        File file = new File(Constant.app_folder + Constant.SelectedAudio_AAC);
        if (file.exists()) {
            audio = new MediaPlayer();
            try {
                audio.setDataSource(Constant.app_folder + Constant.SelectedAudio_AAC);
                audio.prepare();
            } catch (IOException e) {
                e.printStackTrace();
            }

            MediaMetadataRetriever mmr = new MediaMetadataRetriever();
            mmr.setDataSource(this, Uri.fromFile(file));
            String durationStr = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            final int file_duration = Integer.parseInt(durationStr);

            Log.e("file_duration",""+file_duration);

            if (file_duration < Constant.max_recording_duration) {
                Constant.recording_duration = file_duration;
                initlize_Video_progress();
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        cameraView.start();
    }

    @Override
    protected void onDestroy() {
        Release_Resources();
        super.onDestroy();
    }

    public void Pick_video_from_gallery() {
        Intent intent = new Intent(
                Intent.ACTION_PICK,
                android.provider.MediaStore.Video.Media.EXTERNAL_CONTENT_URI);
        intent.setType("video/*");
        startActivityForResult(intent, Constant.Pick_video_from_gallery);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == RESULT_OK) {
            if (requestCode == Sounds_list_Request_code) {
                if (data != null) {

                    if (data.getStringExtra("isSelected").equals("yes")) {
                        txt_add_sound.setText(data.getStringExtra("sound_name"));
                        Constant.Selected_sound_id = data.getStringExtra("sound_id");
                        PreparedAudio();
                    }
                }
            } else if (requestCode == Constant.Pick_video_from_gallery) {
                Uri uri = data.getData();
                try {
                    File video_file = FileUtils.getFileFromUri(this, uri);

                    if (FileUtils.getfileduration(this, uri) < Constant.max_recording_duration) {
                        Chnage_Video_size(video_file.getAbsolutePath(), Constant.gallery_resize_video);
                    } else {
                        Intent intent = new Intent(VideoRecord.this, TrimVideo.class);
                        intent.putExtra("path", video_file.getAbsolutePath());
                        startActivity(intent);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public long getfileduration(Uri uri) {
        try {
            MediaMetadataRetriever mmr = new MediaMetadataRetriever();
            mmr.setDataSource(this, uri);
            String durationStr = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            final int file_duration = Integer.parseInt(durationStr);
            return file_duration;
        } catch (Exception e) {
        }
        return 0;
    }

    public void Chnage_Video_size(String src_path, String destination_path) {
        try {
            Utils.copyFile(new File(src_path),
                    new File(destination_path));

            File file = new File(src_path);
            if (file.exists())
                file.delete();

            Intent intent = new Intent(VideoRecord.this, GallaryActivity.class);
            intent.putExtra("video_path", Constant.gallery_resize_video);
            startActivity(intent);
        } catch (IOException e) {
            e.printStackTrace();
            Log.e(Constant.tag, e.toString());
        }
    }

    public void startTrim(final File src, final File dst, final int startMs, final int endMs) throws IOException {

        new AsyncTask<String, Void, String>() {
            @Override
            protected String doInBackground(String... strings) {
                try {

                    FileDataSourceImpl file = new FileDataSourceImpl(src);
                    Movie movie = MovieCreator.build(file);
                    List<Track> tracks = movie.getTracks();
                    movie.setTracks(new LinkedList<Track>());
                    double startTime = startMs / 1000;
                    double endTime = endMs / 1000;
                    boolean timeCorrected = false;

                    for (Track track : tracks) {
                        if (track.getSyncSamples() != null && track.getSyncSamples().length > 0) {
                            if (timeCorrected) {
                                throw new RuntimeException("The startTime has already been corrected by another track with SyncSample. Not Supported.");
                            }
                            startTime = Utils.correctTimeToSyncSample(track, startTime, false);
                            endTime = Utils.correctTimeToSyncSample(track, endTime, true);
                            timeCorrected = true;
                        }
                    }
                    for (Track track : tracks) {
                        long currentSample = 0;
                        double currentTime = 0;
                        long startSample = -1;
                        long endSample = -1;

                        for (int i = 0; i < track.getSampleDurations().length; i++) {
                            if (currentTime <= startTime) {
                                startSample = currentSample;
                            }
                            if (currentTime <= endTime) {
                                endSample = currentSample;
                            } else {
                                break;
                            }
                            currentTime += (double) track.getSampleDurations()[i] / (double) track.getTrackMetaData().getTimescale();
                            currentSample++;
                        }
                        movie.addTrack(new CroppedTrack(track, startSample, endSample));
                    }

                    Container out = new DefaultMp4Builder().build(movie);
                    MovieHeaderBox mvhd = Path.getPath(out, "moov/mvhd");
                    mvhd.setMatrix(Matrix.ROTATE_180);
                    if (!dst.exists()) {
                        dst.createNewFile();
                    }
                    FileOutputStream fos = new FileOutputStream(dst);
                    WritableByteChannel fc = fos.getChannel();
                    try {
                        out.writeContainer(fc);
                    } finally {
                        fc.close();
                        fos.close();
                        file.close();
                    }

                    file.close();
                    return "Ok";
                } catch (IOException e) {
                    Log.e("trim?", e.toString());
                    return "error";
                }

            }

            @Override
            protected void onPreExecute() {
                super.onPreExecute();
                Utils.Show_determinent_loader(VideoRecord.this, true, true);
            }

            @Override
            protected void onPostExecute(String result) {
                if (result.equals("error")) {
                    Toast.makeText(VideoRecord.this, "Try Again", Toast.LENGTH_SHORT).show();
                } else {
                    Utils.cancel_indeterminent_loader();
                    Chnage_Video_size(Constant.gallery_trimed_video, Constant.gallery_resize_video);
                }
            }

        }.execute();

    }

    public void Release_Resources() {
        try {
            if (audio != null) {
                audio.stop();
                audio.reset();
                audio.release();
            }
            cameraView.stop();
        } catch (Exception e) {

        }
        DeleteFile();
    }

    @Override
    public void onBackPressed() {

        new AlertDialog.Builder(this)
                .setTitle("Alert")
                .setMessage("Are you Sure? if you Go back you can't undo this action")
                .setNegativeButton("No", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                })
                .setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                        Release_Resources();
                        finish();
                        overridePendingTransition(R.anim.in_from_top, R.anim.out_from_bottom);
                    }
                }).show();
    }

    // this will hide the bottom mobile navigation controll
    public void Hide_navigation() {

        requestWindowFeature(Window.FEATURE_NO_TITLE);
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        final int flags = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;

        // This work only for android 4.4+
        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {

            getWindow().getDecorView().setSystemUiVisibility(flags);

            // Code below is to handle presses of Volume up or Volume down.
            // Without this, after pressing volume buttons, the navigation bar will
            // show up and won't hide
            final View decorView = getWindow().getDecorView();
            decorView
                    .setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {

                        @Override
                        public void onSystemUiVisibilityChange(int visibility) {
                            if ((visibility & View.SYSTEM_UI_FLAG_FULLSCREEN) == 0) {
                                decorView.setSystemUiVisibility(flags);
                            }
                        }
                    });
        }

    }

    // if the Recording is stop then it we start the recording
    // and if the mobile is recording the video then it will stop the recording
    public void Start_or_Stop_Recording() {

        if (!is_recording && sec_passed < (Constant.recording_duration / 1000) - 1) {
            number = number + 1;

            is_recording = true;

            File file = new File(Constant.app_showing_folder + "myvideo" + (number) + ".mp4");
            videopaths.add(Constant.app_showing_folder + "myvideo" + (number) + ".mp4");
            cameraView.captureVideo(file);

            if (audio != null)
                audio.start();

            iv_done.setImageDrawable(getResources().getDrawable(R.drawable.ic_not_done));
            iv_done.setEnabled(false);

            video_progress.resume();

            iv_record.setImageDrawable(getResources().getDrawable(R.drawable.ic_recoding_yes));

//            cut_video_btn.setVisibility(View.GONE);

            findViewById(R.id.ly_upload).setEnabled(false);
            camera_options.setVisibility(View.GONE);
            txt_add_sound.setClickable(false);
            camera_options.setVisibility(View.GONE);

        } else if (is_recording) {

            is_recording = false;

            video_progress.pause();
            video_progress.addDivider();

            if (audio != null && audio.isPlaying())
                audio.pause();

            cameraView.stopVideo();

            Check_done_btn_enable();

            findViewById(R.id.ly_upload).setEnabled(true);
            iv_record.setImageDrawable(getResources().getDrawable(R.drawable.ic_recoding_no));
            txt_add_sound.setClickable(true);
            camera_options.setVisibility(View.VISIBLE);

        } else if (sec_passed > (Constant.recording_duration / 1000)) {
            Utils.Show_Alert(this, "Alert", "Video only can be a " + (int) Constant.recording_duration / 1000 + " S");
        }

    }

    public void initlize_Video_progress() {
        sec_passed = 0;
        video_progress = findViewById(R.id.video_progress);
        video_progress.enableAutoProgressView(Constant.recording_duration);
        video_progress.setDividerColor(Color.WHITE);
        video_progress.setDividerEnabled(true);
        video_progress.setDividerWidth(4);
        video_progress.setShader(new int[]{Color.CYAN, Color.CYAN, Color.CYAN});

        video_progress.SetListener(new ProgressBarListener() {
            @Override
            public void TimeinMill(long mills) {
                time_in_milis = mills;
                sec_passed = (int) (mills / 1000);

                if (sec_passed > (Constant.recording_duration / 1000) - 1) {
                    Start_or_Stop_Recording();
                }

                if (is_recording_timer_enable && sec_passed >= recording_time) {
                    is_recording_timer_enable = false;
                    Start_or_Stop_Recording();
                }

            }
        });
    }

    int delete_count = 0;

    public void DeleteFile() {
        delete_count++;
        File output = new File(Constant.outputfile);
        File output2 = new File(Constant.outputfile2);
        File output_filter_file = new File(Constant.output_filter_file);

        if (output.exists()) {
            output.delete();
        }
        if (output2.exists()) {

            output2.delete();
        }
        if (output_filter_file.exists()) {
            output_filter_file.delete();
        }

        File file = new File(Constant.root + "/" + "myvideo" + (delete_count) + ".mp4");
        if (file.exists()) {
            file.delete();
            DeleteFile();
        }

    }

    public void RotateCamera() {
        cameraView.toggleFacing();
    }

    // this will apped all the videos parts in one  fullvideo
    private boolean append() {
        final ProgressDialog progressDialog = new ProgressDialog(VideoRecord.this);
        new Thread(new Runnable() {
            @Override
            public void run() {
                runOnUiThread(new Runnable() {
                    public void run() {
                        progressDialog.setMessage("Please wait..");
                        progressDialog.show();
                    }
                });

                ArrayList<String> video_list = new ArrayList<>();
                for (int i = 0; i < videopaths.size(); i++) {
                    File file = new File(videopaths.get(i));
                    if (file.exists()) {
                        try {
                            MediaMetadataRetriever retriever = new MediaMetadataRetriever();
                            retriever.setDataSource(VideoRecord.this, Uri.fromFile(file));
                            String hasVideo = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_HAS_VIDEO);
                            boolean isVideo = "yes".equals(hasVideo);
                            if (isVideo && file.length() > 3000) {
                                Log.e("resp", videopaths.get(i));
                                video_list.add(videopaths.get(i));
                            }
                        } catch (Exception e) {
                            Log.e(Constant.tag, e.toString());
                        }
                    }
                }

                try {
                    Movie[] inMovies = new Movie[video_list.size()];
                    for (int i = 0; i < video_list.size(); i++) {
                        inMovies[i] = MovieCreator.build(video_list.get(i));
                    }
                    List<Track> videoTracks = new LinkedList<Track>();
                    List<Track> audioTracks = new LinkedList<Track>();
                    for (Movie m : inMovies) {
                        for (Track t : m.getTracks()) {
                            if (t.getHandler().equals("soun")) {
                                audioTracks.add(t);
                            }
                            if (t.getHandler().equals("vide")) {
                                videoTracks.add(t);
                            }
                        }
                    }
                    Movie result = new Movie();
                    if (audioTracks.size() > 0) {
                        result.addTrack(new AppendTrack(audioTracks.toArray(new Track[audioTracks.size()])));
                    }
                    if (videoTracks.size() > 0) {
                        result.addTrack(new AppendTrack(videoTracks.toArray(new Track[videoTracks.size()])));
                    }

                    Container out = new DefaultMp4Builder().build(result);

                    String outputFilePath = null;
                    if (cameraView.isFacingFront()) {
                        outputFilePath = Constant.output_frontcamera;
                    } else {
                        if (audio != null)
                            outputFilePath = Constant.outputfile;
                        else
                            outputFilePath = Constant.outputfile2;
                    }

                    FileOutputStream fos = new FileOutputStream(new File(outputFilePath));
                    out.writeContainer(fos.getChannel());
                    fos.close();

                    runOnUiThread(new Runnable() {
                        public void run() {
                            progressDialog.dismiss();

                            if (cameraView.isFacingFront()) {
                                if (audio != null)
                                    Change_fliped_video(Constant.output_frontcamera, Constant.outputfile);
                                else
                                    Change_fliped_video(Constant.output_frontcamera, Constant.outputfile2);
                            } else {
                                if (audio != null)
                                    Merge_withAudio();
                                else {
                                    Go_To_preview_Activity();
                                }
                            }
                        }
                    });

                } catch (Exception e) {
                    Log.e("Exception-append", "" + e.getMessage());
                }
            }
        }).start();

        return true;
    }

    // this will add the select audio with the video
    public void Merge_withAudio() {

        String audio_file = Constant.app_folder + Constant.SelectedAudio_AAC;

        MergeVideoAudio merge_video_audio = new MergeVideoAudio(VideoRecord.this);
        merge_video_audio.doInBackground(audio_file, Constant.outputfile, Constant.outputfile2);

    }

    public void Go_To_preview_Activity() {
        Intent intent = new Intent(this, PreviewVideo.class);
        startActivity(intent);
        overridePendingTransition(R.anim.in_from_right, R.anim.out_to_left);
    }

    public void Check_done_btn_enable() {
        if (sec_passed > (Constant.min_time_recording / 1000)) {
            iv_done.setImageDrawable(getResources().getDrawable(R.drawable.ic_done));
            iv_done.setEnabled(true);
        } else {
            iv_done.setImageDrawable(getResources().getDrawable(R.drawable.ic_not_done));
            iv_done.setEnabled(false);
        }
    }

    public void Change_fliped_video(String srcMp4Path, final String destMp4Path) {

        Utils.Show_determinent_loader(this, false, false);
        new GPUMp4Composer(srcMp4Path, destMp4Path)
                .flipHorizontal(true)
                .listener(new GPUMp4Composer.Listener() {
                    @Override
                    public void onProgress(double progress) {
                        Log.e("resp", "" + (int) (progress * 100));
                        Utils.Show_loading_progress((int) (progress * 100));
                    }

                    @Override
                    public void onCompleted() {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {

                                Utils.cancel_determinent_loader();
                                if (audio != null)
                                    Merge_withAudio();
                                else {
                                    Go_To_preview_Activity();
                                }
                            }
                        });
                    }

                    @Override
                    public void onCanceled() {
                        Log.d("resp", "onCanceled");
                    }

                    @Override
                    public void onFailed(Exception exception) {

                        Log.e("resp", exception.getMessage());
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    Utils.cancel_determinent_loader();

                                    Toast.makeText(VideoRecord.this, "Try Again", Toast.LENGTH_SHORT).show();
                                } catch (Exception e) {
                                }
                            }
                        });
                    }
                })
                .start();

    }

}
