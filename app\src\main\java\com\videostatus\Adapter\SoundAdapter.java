package com.videostatus.Adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.github.ybq.android.spinkit.SpinKitView;
import com.squareup.picasso.Picasso;
import com.videostatus.Model.SoundModel.Result;
import com.videostatus.R;
import com.videostatus.Utility.Constant;

import java.util.List;

public class SoundAdapter extends RecyclerView.Adapter<SoundAdapter.CustomViewHolder> {
    public Context context;
    List<Result> datalist;
    public OnItemClickListener listener;

    public interface OnItemClickListener {
        void onItemClick(View view, int postion, Result item);
    }

    public SoundAdapter(Context context, List<Result> arrayList, OnItemClickListener listener) {
        this.context = context;
        datalist = arrayList;
        this.listener = listener;
    }

    @Override
    public CustomViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.sound_item_layout, parent, false);
        return new SoundAdapter.CustomViewHolder(itemView);
    }


    @Override
    public int getItemCount() {
        return datalist.size();
    }


    @Override
    public void onBindViewHolder(final CustomViewHolder holder, final int i) {
        holder.setIsRecyclable(false);

        Result item = datalist.get(i);
        try {
            holder.bind(i, datalist.get(i), listener);
            holder.txt_sound_name.setText(item.getTitle());

            Picasso.with(context).load(item.getThumbnail())
                    .placeholder(context.getResources().getDrawable(R.drawable.image_placeholder))
                    .into(holder.iv_sound);

        } catch (Exception e) {

        }
    }

    class CustomViewHolder extends RecyclerView.ViewHolder {

        public TextView txt_play, txt_sound_name, txt_pause;
        ImageView iv_sound, iv_done;
        SpinKitView sp_loading;

        public CustomViewHolder(View view) {
            super(view);

            txt_sound_name = view.findViewById(R.id.txt_sound_name);
            txt_play = view.findViewById(R.id.txt_play);
            txt_pause = view.findViewById(R.id.txt_pause);
            iv_sound = view.findViewById(R.id.iv_sound);
            iv_done = view.findViewById(R.id.iv_done);
            sp_loading = view.findViewById(R.id.sp_loading);

        }

        public void bind(final int pos, final Result item, final OnItemClickListener listener) {

            txt_play.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(v, pos, item);
                }
            });

            iv_done.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(v, pos, item);
                }
            });

            txt_sound_name.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(v, pos, item);
                }
            });
        }
    }
}

