package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.makeramen.roundedimageview.RoundedImageView;
import com.squareup.picasso.Picasso;
import com.videostatus.Activity.Profile;
import com.videostatus.Model.UserModel.Result;
import com.videostatus.R;

import java.util.List;

import static com.squareup.picasso.Picasso.Priority.HIGH;

public class UserAdapter extends RecyclerView.Adapter<UserAdapter.MyViewHolder> {

    private List<Result> UserList;
    Context mcontext;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        public TextView title, txt_mobile;
        RoundedImageView iv_thumb;
        CardView cardview;

        public MyViewHolder(View view) {
            super(view);
            title = view.findViewById(R.id.title);
            txt_mobile = view.findViewById(R.id.txt_mobile);
            iv_thumb = view.findViewById(R.id.iv_thumb);
            cardview = view.findViewById(R.id.cardview);
        }
    }


    public UserAdapter(Context context, List<Result> UserList) {
        this.UserList = UserList;
        this.mcontext = context;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.user_item_row, parent, false);

        return new MyViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        holder.title.setText(UserList.get(position).getFullname());
        holder.txt_mobile.setText(UserList.get(position).getTotalPoints()+" PTS");

        Picasso.with(mcontext).load(""+UserList.get(position).getProfileImg())
                .resize(400, 400)
                .placeholder(R.drawable.no_user).centerInside().priority(HIGH)
                .into(holder.iv_thumb);

        holder.cardview.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Log.e("click", "call");
                Intent intent = new Intent(mcontext, Profile.class);
                intent.putExtra("Id", "" + UserList.get(position).getId());
                intent.putExtra("TO_Id", "" + UserList.get(position).getId());
                mcontext.startActivity(intent);
            }
        });
    }

    @Override
    public int getItemCount() {
        return UserList.size();
    }

}
