package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.squareup.picasso.Picasso;
import com.videostatus.Activity.Profile;
import com.videostatus.Activity.StatusDetails;
import com.videostatus.Model.CommentModel.Result;
import com.videostatus.R;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Random;

import static com.squareup.picasso.Picasso.Priority.HIGH;

public class CommentAdapter extends RecyclerView.Adapter<CommentAdapter.MyViewHolder> {

    private List<Result> CommentList;
    Context mcontext;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        public TextView txt_user_name, txt_comment, txt_date, txt_tag;
        ImageView iv_thumb;

        public MyViewHolder(View view) {
            super(view);
            txt_user_name = (TextView) view.findViewById(R.id.txt_user_name);
            txt_comment = (TextView) view.findViewById(R.id.txt_comment);
            txt_date = (TextView) view.findViewById(R.id.txt_date);
            iv_thumb = (ImageView) view.findViewById(R.id.iv_thumb);
            txt_tag = (TextView) view.findViewById(R.id.txt_tag);
        }
    }


    public CommentAdapter(Context context, List<Result> CommentList) {
        this.CommentList = CommentList;
        this.mcontext = context;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.comment_item, parent, false);

        return new MyViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        holder.txt_user_name.setText("" + CommentList.get(position).getFullname());
        holder.txt_comment.setText("" + CommentList.get(position).getComment());
        Picasso.with(mcontext).load("" + CommentList.get(position).getProfileImg()).priority(HIGH).into(holder.iv_thumb);

        try {
            SimpleDateFormat dateFormat = dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date sourceDate = sourceDate = dateFormat.parse(CommentList.get(position).getCDate());
            SimpleDateFormat targetFormat = new SimpleDateFormat("dd-MMM-yy HH:mm a");
            String targetdatevalue = targetFormat.format(sourceDate);
            holder.txt_date.setText("" + targetdatevalue);
        } catch (Exception e) {
            Log.e("Exception", "" + e.getMessage());
        }

        holder.iv_thumb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Log.e("click", "call");
                Intent intent = new Intent(mcontext, Profile.class);
                intent.putExtra("Id", "" + CommentList.get(position).getUserId());
                mcontext.startActivity(intent);
            }
        });

    }

    @Override
    public int getItemCount() {
        return CommentList.size();
    }

}
