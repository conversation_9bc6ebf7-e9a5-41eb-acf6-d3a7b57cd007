<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:keepScreenOn="true"
    tools:context=".Activity.VideoRecord.PostVideo">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:background="@color/white"
            app:contentInsetLeft="0dp"
            app:contentInsetStart="0dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/d_bottom_gray_line">

                <TextView
                    android:id="@+id/username"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="10dp"
                    android:text="Post"
                    android:textColor="@color/black"
                    android:textSize="18dp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_back"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="15dp"
                    android:background="@color/transparent"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_back"
                    android:tint="@color/black" />

            </RelativeLayout>

        </androidx.appcompat.widget.Toolbar>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/toolbar"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:background="@drawable/d_bottom_gray_line"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:weightSum="1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center">

                    <ImageView
                        android:id="@+id/iv_thumbnail"
                        android:layout_width="200dp"
                        android:layout_height="match_parent"
                        android:layout_margin="10dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/image_placeholder" />
                </LinearLayout>

            </LinearLayout>

            <EditText
                android:id="@+id/et_description"
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:layout_margin="@dimen/margin_10"
                android:layout_weight="0.7"
                android:background="@drawable/d_bottom_gray_line"
                android:gravity="start"
                android:hint="Describe your video"
                android:imeOptions="actionDone"
                android:paddingStart="10dp"
                android:textColor="@color/font_dark" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal"
            android:weightSum="2">

            <LinearLayout
                android:id="@+id/btn_draft"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginEnd="5dp"
                android:layout_weight="1"
                android:background="@drawable/d_gray_border"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginStart="5dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_drafts" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:text="Draft"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/btn_post"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginStart="5dp"
                android:layout_weight="1"
                android:background="@color/redcolor"
                android:gravity="center">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_upload" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:text="Post Video"
                    android:textColor="@color/white"
                    android:textSize="14dp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>
