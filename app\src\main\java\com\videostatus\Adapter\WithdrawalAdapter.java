package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import com.videostatus.Activity.VideoList;
import com.videostatus.Model.WithDrawalModel.Result;
import com.videostatus.R;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class WithdrawalAdapter extends RecyclerView.Adapter<WithdrawalAdapter.MyViewHolder> {

    private List<Result> WithdrawalList;
    Context mcontext;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        public TextView txt_title, txt_date, txt_desc, txt_points;
        ImageView iv_thumb;

        public MyViewHolder(View view) {
            super(view);
            txt_title = view.findViewById(R.id.txt_title);
            txt_date = view.findViewById(R.id.txt_date);
            txt_desc = view.findViewById(R.id.txt_desc);
            txt_points = view.findViewById(R.id.txt_points);
            iv_thumb = view.findViewById(R.id.iv_thumb);
        }
    }


    public WithdrawalAdapter(Context context, List<Result> WithdrawalList) {
        this.WithdrawalList = WithdrawalList;
        this.mcontext = context;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.withdrawal_item_row, parent, false);

        return new MyViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        holder.txt_title.setText(WithdrawalList.get(position).getPaymentDetail());
        holder.txt_points.setText(""+WithdrawalList.get(position).getTotalAmount()+" USD");

        try {
            SimpleDateFormat dateFormat = dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date sourceDate = sourceDate = dateFormat.parse(WithdrawalList.get(position).getCreatedAt());
            SimpleDateFormat targetFormat = new SimpleDateFormat("yy-MMM-dd HH:mm a");
            String targetdatevalue = targetFormat.format(sourceDate);
            holder.txt_date.setText("" + targetdatevalue);
        } catch (Exception e) {
            Log.e("Exception", "" + e.getMessage());
        }
    }

    @Override
    public int getItemCount() {
        return WithdrawalList.size();
    }

}
