<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/lib/com.google.ads"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:orientation="vertical">


                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    android:background="?attr/colorPrimary"
                    android:visibility="gone"
                    app:popupTheme="@style/AppTheme.PopupOverlay"
                    app:titleTextColor="@color/white" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <com.potyvideo.library.AndExoPlayerView
                            android:id="@+id/andExoPlayerView"
                            android:layout_width="match_parent"
                            android:layout_height="220dp"
                            android:visibility="gone"
                            app:andexo_aspect_ratio="aspect_match"
                            app:andexo_full_screen="true"
                            app:andexo_play_when_ready="false"
                            app:andexo_resize_mode="Fit" />

                        <ImageView
                            android:id="@+id/iv_thumb"
                            android:layout_width="match_parent"
                            android:layout_height="250dp"
                            android:scaleType="fitXY" />

                        <ImageView
                            android:id="@+id/iv_play"
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:layout_centerInParent="true"
                            app:srcCompat="@drawable/ic_play" />

                        <TextView
                            android:id="@+id/txt_back"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_margin="15dp"
                            android:background="@drawable/ic_back" />

                        <TextView
                            android:id="@+id/txt_fav"
                            android:layout_width="25dp"
                            android:layout_height="25dp"
                            android:layout_alignParentEnd="true"
                            android:layout_margin="@dimen/margin_15"
                            android:background="@drawable/ic_bookmark" />

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="100dp"
                            android:weightSum="1.0">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.25"
                                android:gravity="center">

                                <com.makeramen.roundedimageview.RoundedImageView
                                    android:id="@+id/iv_user_thumb"
                                    android:layout_width="70dp"
                                    android:layout_height="70dp"
                                    android:elevation="5dp"
                                    android:scaleType="fitXY"
                                    app:riv_border_color="@color/white"
                                    app:riv_border_width="2dip"
                                    app:riv_mutate_background="true"
                                    app:riv_oval="true"
                                    app:riv_tile_mode="clamp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.62"
                                android:gravity="center_vertical"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/txt_title"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/public_semibold"
                                    android:gravity="center"
                                    android:singleLine="true"
                                    android:textColor="@color/font_black"
                                    android:textIsSelectable="true"
                                    android:textSize="@dimen/text_14" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="7dp"
                                    android:weightSum="1.0">

                                    <com.iarcuschin.simpleratingbar.SimpleRatingBar
                                        android:id="@+id/ratingbar"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        app:srb_borderColor="@color/rating_fill"
                                        app:srb_fillColor="@color/rating_fill"
                                        app:srb_numberOfStars="5"
                                        app:srb_rating="3"
                                        app:srb_starSize="15dp" />

                                    <TextView
                                        android:id="@+id/txt_avg"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="5dp"
                                        android:fontFamily="@font/public_medium"
                                        android:gravity="center"
                                        android:singleLine="true"
                                        android:textColor="@color/font_dark"
                                        android:textIsSelectable="true"
                                        android:textSize="@dimen/text_12" />

                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="7dp"
                                    android:weightSum="1.0">

                                    <LinearLayout
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="0.7">

                                        <TextView
                                            android:id="@+id/txt_view"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:fontFamily="@font/public_medium"
                                            android:gravity="center"
                                            android:singleLine="true"
                                            android:textColor="@color/font_dark"
                                            android:textSize="@dimen/text_12" />

                                    </LinearLayout>


                                </LinearLayout>

                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/ly_share"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.13"
                                android:gravity="center">

                                <ImageView
                                    android:layout_width="25dp"
                                    android:layout_height="25dp"
                                    android:background="@drawable/ic_share" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="45dp"
                            android:layout_margin="5dp"
                            android:gravity="center_vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1">

                                <LinearLayout
                                    android:id="@+id/ly_like"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="5dp"
                                    android:background="@drawable/round_border_gray"
                                    android:gravity="center">

                                    <TextView
                                        android:layout_width="18dp"
                                        android:layout_height="18dp"
                                        android:background="@drawable/ic_like"
                                        android:gravity="center" />

                                    <TextView
                                        android:id="@+id/txt_like_cnt"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_marginLeft="5dp"
                                        android:fontFamily="@font/public_regular"
                                        android:gravity="center_vertical"
                                        android:text="10 Likes"
                                        android:textSize="12dp" />

                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1">

                                <LinearLayout
                                    android:id="@+id/ly_dislike"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="5dp"
                                    android:background="@drawable/round_border_gray"
                                    android:gravity="center">

                                    <TextView
                                        android:layout_width="18dp"
                                        android:layout_height="18dp"
                                        android:layout_marginTop="2dp"
                                        android:background="@drawable/ic_dislike"
                                        android:gravity="center" />

                                    <TextView
                                        android:id="@+id/txt_dislike_cnt"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_marginLeft="5dp"
                                        android:fontFamily="@font/public_medium"
                                        android:gravity="center_vertical"
                                        android:text="10 Dislikes"
                                        android:textSize="12dp" />

                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1">

                                <LinearLayout
                                    android:id="@+id/ly_download"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_margin="5dp"
                                    android:background="@drawable/round_border_gray"
                                    android:gravity="center"
                                    android:weightSum="1.0">

                                    <TextView
                                        android:layout_width="15dp"
                                        android:layout_height="15dp"
                                        android:background="@drawable/ic_download"
                                        android:gravity="center" />

                                    <TextView
                                        android:id="@+id/txt_download_cnt"
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_marginLeft="5dp"
                                        android:fontFamily="@font/public_medium"
                                        android:gravity="center_vertical"
                                        android:text="10 Download"
                                        android:textSize="11dp" />

                                </LinearLayout>

                            </LinearLayout>

                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:layout_marginTop="10dp"
                            android:background="@color/font_dark" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginTop="15dp"
                            android:gravity="center">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1.2"></LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:gravity="center">

                                <ImageView
                                    android:id="@+id/iv_whatsapp"
                                    android:layout_width="@dimen/icon"
                                    android:layout_height="@dimen/icon"
                                    android:layout_centerInParent="true"
                                    android:background="@drawable/ic_whatsapp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:gravity="center">

                                <ImageView
                                    android:id="@+id/iv_fb"
                                    android:layout_width="@dimen/icon"
                                    android:layout_height="@dimen/icon"
                                    android:background="@drawable/ic_fb_status" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:gravity="center">

                                <ImageView
                                    android:id="@+id/iv_insta"
                                    android:layout_width="@dimen/icon"
                                    android:layout_height="@dimen/icon"
                                    android:layout_centerInParent="true"
                                    android:background="@drawable/ic_instagram" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:gravity="center">

                                <ImageView
                                    android:id="@+id/iv_twitter"
                                    android:layout_width="@dimen/icon"
                                    android:layout_height="@dimen/icon"
                                    android:layout_centerInParent="true"
                                    android:background="@drawable/ic_twitter" />
                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1.2"></LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical"
                        android:weightSum="1.0">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="30dp"
                                android:layout_marginTop="10dp"
                                android:gravity="center_vertical"
                                android:orientation="horizontal"
                                android:weightSum="1.0">

                                <TextView
                                    android:layout_width="0dp"
                                    android:layout_height="match_parent"
                                    android:layout_marginLeft="5dp"
                                    android:layout_weight="0.8"
                                    android:fontFamily="@font/public_medium"
                                    android:gravity="center_vertical"
                                    android:text="Related Videos"
                                    android:textColor="@color/colorPrimary"
                                    android:textSize="14dp" />

                            </LinearLayout>

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recycler_related"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_margin="3dp"
                                android:nestedScrollingEnabled="true"
                                android:scrollbars="none"
                                android:visibility="visible" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:orientation="vertical">

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_margin="5dp"
                            android:background="@color/gray_light" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center_vertical"
                            android:paddingStart="5dp"
                            android:text="@string/Comments"
                            android:textColor="@color/colorPrimary"
                            android:textSize="14dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="45dp"
                            android:weightSum="1.0">

                            <EditText
                                android:id="@+id/et_comment"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_marginLeft="5dp"
                                android:layout_marginRight="5dp"
                                android:layout_weight="1"
                                android:background="@drawable/round_comment"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center_vertical"
                                android:hint="@string/Write_your_comments"
                                android:paddingLeft="10dp"
                                android:textColorHint="@color/font_dark"
                                android:textSize="14dp" />

                            <LinearLayout
                                android:id="@+id/ly_send"
                                android:layout_width="45dp"
                                android:layout_height="match_parent"
                                android:layout_marginRight="5dp"
                                android:background="@drawable/round_send"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:background="@drawable/ic_send" />

                            </LinearLayout>
                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_comment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="3dp"
                            android:fontFamily="@font/public_bold"
                            android:minHeight="@dimen/min_height"
                            android:scrollbars="none"
                            android:visibility="visible" />
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>