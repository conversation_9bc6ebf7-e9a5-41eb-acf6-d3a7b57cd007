package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.squareup.picasso.Picasso;
import com.videostatus.Activity.HomeSingalVideo;
import com.videostatus.Model.VideoModel.Result;
import com.videostatus.R;
import com.videostatus.Utility.Constant;
import com.videostatus.Interface.ShareAll;

import java.util.List;

public class UploadedAdapter extends RecyclerView.Adapter<UploadedAdapter.MyViewHolder> {

    private List<Result> UplaodedList;
    Context mcontext;
    ShareAll shareAll;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        TextView txt_bookmark;
        ImageView iv_thumb;
        CardView cardview;

        public MyViewHolder(View view) {
            super(view);
            txt_bookmark = (TextView) view.findViewById(R.id.txt_bookmark);
            iv_thumb = (ImageView) view.findViewById(R.id.iv_thumb);
            cardview = (CardView) view.findViewById(R.id.cardview);
        }
    }

    public UploadedAdapter(Context context, List<Result> UplaodedList, ShareAll shareAll) {
        this.UplaodedList = UplaodedList;
        this.mcontext = context;
        this.shareAll = shareAll;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.uploaded_item_row, parent, false);

        return new MyViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        Picasso.with(mcontext).load(UplaodedList.get(position).getThumbnailImg()).resize(400, 400)
                .placeholder(R.drawable.no_image).centerCrop().into(holder.iv_thumb);

        holder.cardview.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Constant.VideoList = UplaodedList;
                Intent intent = new Intent(mcontext, HomeSingalVideo.class);
                intent.putExtra("position", position);
                mcontext.startActivity(intent);
            }
        });
    }

    @Override
    public int getItemCount() {
        return UplaodedList.size();
    }

    public String withSuffix(long count) {
        if (count < 1000) return "" + count;
        int exp = (int) (Math.log(count) / Math.log(1000));
        return String.format("%.1f %c",
                count / Math.pow(1000, exp),
                "kMGTPE".charAt(exp - 1));
    }
}
