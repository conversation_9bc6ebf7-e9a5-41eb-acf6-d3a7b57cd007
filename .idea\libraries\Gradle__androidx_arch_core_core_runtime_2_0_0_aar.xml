<component name="libraryTable">
  <library name="Gradle: androidx.arch.core:core-runtime:2.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/1e93a91a4e40c8e3a5bbf7aa505aa5dd/core-runtime-2.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/1e93a91a4e40c8e3a5bbf7aa505aa5dd/core-runtime-2.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/1e93a91a4e40c8e3a5bbf7aa505aa5dd/core-runtime-2.0.0/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-runtime/2.0.0/bc41b287c95bc50a3cd27cb1b7cfb301805ba7f1/core-runtime-2.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>