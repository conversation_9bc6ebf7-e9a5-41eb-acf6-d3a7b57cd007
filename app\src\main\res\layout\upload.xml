<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical"
        android:weightSum="1.0">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white"
            app:titleTextColor="@color/colorPrimary"
            app:popupTheme="@style/AppTheme.PopupOverlay" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="0.9"
            android:orientation="vertical">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/ly_category_viewall"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:weightSum="1.0">

                    <LinearLayout
                        android:id="@+id/ly_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_horizontal"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginRight="10dp"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center_vertical"
                            android:text="| Video Title"
                            android:textColor="@color/colorPrimary"
                            android:textSize="14dp" />

                        <EditText
                            android:id="@+id/et_video_title"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="5dp"
                            android:layout_marginRight="10dp"
                            android:background="@drawable/edittext_bg"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center_vertical"
                            android:hint="Video Title"
                            android:paddingLeft="5dp"
                            android:textColor="@color/font_dark"
                            android:textSize="12dp" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="10dp"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center_vertical"
                        android:text="| Categories"
                        android:textColor="@color/colorPrimary"
                        android:textSize="14dp" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <Spinner
                            android:id="@+id/spinner1"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:layout_marginLeft="10dp"
                            android:layout_marginRight="10dp"
                            android:background="@drawable/edittext_bg"
                            android:spinnerMode="dropdown" />

                        <ImageView
                            android:id="@+id/imageView2"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerInParent="true"
                            android:layout_marginRight="20dp"
                            app:srcCompat="@drawable/ic_down_arrow" />

                    </RelativeLayout>

                    <LinearLayout
                        android:id="@+id/ly_image"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="10dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/txt_browse_image"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_marginStart="30dp"
                            android:layout_marginEnd="30dp"
                            android:background="@color/colorPrimary"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center"
                            android:text="Add Thumbnail File"
                            android:textColor="@color/white"
                            android:textSize="14dp" />

                        <ImageView
                            android:id="@+id/iv_browse"
                            android:layout_width="120dp"
                            android:layout_height="90dp"
                            android:layout_marginTop="10dp"
                            android:layout_marginBottom="10dp"
                            android:background="@drawable/no_image"
                            android:scaleType="fitXY" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/ly_video"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="20dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/txt_browse_video"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_marginStart="30dp"
                            android:layout_marginEnd="30dp"
                            android:background="@color/colorPrimary"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center"
                            android:text="Add Video File"
                            android:textColor="@color/white"
                            android:textSize="14dp" />

                        <TextView
                            android:id="@+id/txt_browse_video_file"
                            android:layout_width="match_parent"
                            android:layout_height="35dp"
                            android:layout_marginStart="30dp"
                            android:layout_marginEnd="30dp"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center"
                            android:text="select maximum 15 mb and 30 second video duration"
                            android:textColor="@color/font_dark"
                            android:textSize="14dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="25dp"
                        android:layout_marginBottom="10dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/txt_submit"
                            android:layout_width="150dp"
                            android:layout_height="40dp"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:background="@drawable/round_bor_bg"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center"
                            android:text="UPLOAD"
                            android:textColor="@color/white"
                            android:textSize="12dp" />
                    </LinearLayout>
                </LinearLayout>
            </ScrollView>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="0.1">

            <com.google.android.gms.ads.AdView
                android:id="@+id/adView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:adSize="SMART_BANNER"
                app:adUnitId="@string/admob_banner_ads" />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>