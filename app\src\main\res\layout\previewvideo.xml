<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <com.videostatus.Utility.MovieWrapperView
        android:id="@+id/layout_movie_wrapper"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="25dp"
        android:background="@drawable/ic_back"
        android:backgroundTint="@color/white"
        android:padding="3dp"
        android:scaleType="fitCenter" />

    <TextView
        android:id="@+id/next_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/recylerview"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="15dp"
        android:background="@drawable/round_bor_bg"
        android:paddingLeft="20dp"
        android:paddingTop="10dp"
        android:paddingRight="20dp"
        android:paddingBottom="10dp"
        android:text="Next"
        android:textColor="@color/white"
        android:textSize="15dp"
        android:textStyle="bold" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recylerview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/fifty_transparent_black"
        android:paddingBottom="20dp" />


</RelativeLayout>
