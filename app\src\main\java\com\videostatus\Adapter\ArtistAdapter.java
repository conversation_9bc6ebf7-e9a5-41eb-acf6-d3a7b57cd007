package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.makeramen.roundedimageview.RoundedImageView;
import com.squareup.picasso.Picasso;
import com.videostatus.Activity.Profile;
import com.videostatus.Model.TopUsersModel.Result;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;

import java.util.List;

import static com.squareup.picasso.Picasso.Priority.HIGH;

public class ArtistAdapter extends RecyclerView.Adapter<ArtistAdapter.MyViewHolder> {

    private List<Result> AuthorList;
    Context mcontext;
    String type;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        public TextView txt_bookname;
        RoundedImageView iv_thumb;

        public MyViewHolder(View view) {
            super(view);
            txt_bookname = (TextView) view.findViewById(R.id.txt_bookname);
            iv_thumb = (RoundedImageView) view.findViewById(R.id.iv_thumb);
        }
    }


    public ArtistAdapter(Context context, List<Result> AuthorList, String type) {
        this.AuthorList = AuthorList;
        this.mcontext = context;
        this.type = type;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView;
        if (type.equalsIgnoreCase("fragment")) {
            itemView = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.artist_item, parent, false);
            return new MyViewHolder(itemView);
        } else {
            itemView = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.artist_item, parent, false);
            return new MyViewHolder(itemView);
        }
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        holder.txt_bookname.setText("" + AuthorList.get(position).getFullname());

        Picasso.with(mcontext).load(""+AuthorList.get(position).getProfileImg())
                .into(holder.iv_thumb);

        holder.iv_thumb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(mcontext, Profile.class);
                intent.putExtra("Id", "" + AuthorList.get(position).getId());
                intent.putExtra("TO_Id", "" + AuthorList.get(position).getId());
                mcontext.startActivity(intent);
            }
        });
    }

    @Override
    public int getItemCount() {
        return AuthorList.size();
    }

}
