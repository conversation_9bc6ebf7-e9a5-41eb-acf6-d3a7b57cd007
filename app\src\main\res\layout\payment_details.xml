<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white"
            app:popupTheme="@style/AppTheme.PopupOverlay"
            app:titleTextColor="@color/colorPrimary">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/ly_back"
                    android:layout_width="30dp"
                    android:layout_height="match_parent">

                    <TextView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_back"
                        android:backgroundTint="@color/colorPrimary"
                        android:gravity="center"
                        android:textSize="16dp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/toolbar_title"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="10dp"
                    android:gravity="center"
                    android:text="@string/payment_details"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16dp" />
            </LinearLayout>
        </androidx.appcompat.widget.Toolbar>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/margin_20"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txt_user_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="5dp"
                android:fontFamily="@font/public_regular"
                android:gravity="center_vertical"
                android:text="Select Payment Type"
                android:textColor="@color/font_dark"
                android:textSize="16dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/round_border_gray">

                <RadioGroup
                    android:id="@+id/radioGroup"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp">

                    <RadioButton
                        android:id="@+id/rb_paypal"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:checked="true"
                        android:text=" Paypal"
                        android:fontFamily="@font/public_regular"
                        android:textColor="@color/font_dark"
                        android:textSize="16dp" />

                    <RadioButton
                        android:id="@+id/rb_paytm"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:checked="false"
                        android:text="  Paytm"
                        android:fontFamily="@font/public_regular"
                        android:textColor="@color/font_dark"
                        android:textSize="16dp" />

                    <RadioButton
                        android:id="@+id/rb_wire"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:checked="false"
                        android:text="  WireTransfer / Bank Details"
                        android:fontFamily="@font/public_regular"
                        android:textColor="@color/font_dark"
                        android:textSize="16dp"/>

                </RadioGroup>
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="5dp"
                android:fontFamily="@font/public_regular"
                android:gravity="center_vertical"
                android:text="Enter Payment Details"
                android:textColor="@color/font_dark"
                android:textSize="16dp" />

            <EditText
                android:id="@+id/et_payment_details"
                android:layout_width="match_parent"
                android:layout_height="180dp"
                android:gravity="center_vertical|top"
                android:padding="8dp"
                android:layout_marginTop="15dp"
                android:background="@drawable/round_border_gray" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="25dp"
                android:gravity="center">

                <TextView
                    android:id="@id/txt_submit"
                    android:layout_width="180dp"
                    android:layout_height="45dp"
                    android:background="@drawable/round_bor_bg"
                    android:fontFamily="@font/public_medium"
                    android:gravity="center"
                    android:text="Submit"
                    android:textColor="@color/white"
                    android:textSize="16dp" />
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/rl_adView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginTop="@dimen/margin_20"
                android:background="@color/white"
                android:visibility="visible"></RelativeLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>