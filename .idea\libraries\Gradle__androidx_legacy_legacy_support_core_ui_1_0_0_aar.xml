<component name="libraryTable">
  <library name="Gradle: androidx.legacy:legacy-support-core-ui:1.0.0@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/6f4632dc6509128587af653f30a3112f/legacy-support-core-ui-1.0.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/6f4632dc6509128587af653f30a3112f/legacy-support-core-ui-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/6f4632dc6509128587af653f30a3112f/legacy-support-core-ui-1.0.0/res" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.legacy/legacy-support-core-ui/1.0.0/f6044eaebe354c778f1f147ddb9e92a3f1e22fc7/legacy-support-core-ui-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>