<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:background="@color/gray_light"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/margin_10"
                android:layout_marginTop="@dimen/margin_10"
                android:layout_marginRight="@dimen/margin_10"
                android:layout_marginBottom="@dimen/margin_10">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/margin_10">

                    <EditText
                        android:id="@+id/et_firstname"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/margin_10"
                        android:layout_marginTop="@dimen/margin_20"
                        android:layout_marginRight="@dimen/margin_10"
                        android:background="@color/gray_light"
                        android:gravity="left"
                        android:hint="Firstname"
                        android:imeOptions="actionNext"
                        android:padding="12dp"
                        android:singleLine="true"
                        android:textColor="@android:color/black"
                        android:textCursorDrawable="@drawable/login_signup_cursor"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/et_lastname"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/margin_10"
                        android:layout_marginTop="@dimen/margin_10"
                        android:layout_marginRight="@dimen/margin_10"
                        android:background="@color/gray_light"
                        android:gravity="left"
                        android:hint="Lastname"
                        android:imeOptions="actionNext"
                        android:padding="12dp"
                        android:singleLine="true"
                        android:textColor="@android:color/black"
                        android:textCursorDrawable="@drawable/login_signup_cursor"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/et_email"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/margin_10"
                        android:layout_marginTop="@dimen/margin_10"
                        android:layout_marginRight="@dimen/margin_10"
                        android:background="@color/gray_light"
                        android:gravity="left"
                        android:hint="Email"
                        android:imeOptions="actionNext"
                        android:inputType="textEmailAddress"
                        android:padding="12dp"
                        android:singleLine="true"
                        android:textColor="@android:color/black"
                        android:textCursorDrawable="@drawable/login_signup_cursor"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/et_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/margin_10"
                        android:layout_marginTop="@dimen/margin_10"
                        android:layout_marginRight="@dimen/margin_10"
                        android:background="@color/gray_light"
                        android:gravity="left"
                        android:hint="Password"
                        android:imeOptions="actionNext"
                        android:inputType="textPassword"
                        android:padding="12dp"
                        android:singleLine="true"
                        android:textColor="@android:color/black"
                        android:textCursorDrawable="@drawable/login_signup_cursor"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/et_confirm_pass"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/margin_10"
                        android:layout_marginTop="@dimen/margin_10"
                        android:layout_marginRight="@dimen/margin_10"
                        android:background="@color/gray_light"
                        android:gravity="left"
                        android:hint="Confirm Password"
                        android:imeOptions="actionDone"
                        android:inputType="textPassword"
                        android:padding="12dp"
                        android:singleLine="true"
                        android:textColor="@android:color/black"
                        android:textCursorDrawable="@drawable/login_signup_cursor"
                        android:textSize="14sp" />

                    <Button
                        android:id="@+id/btn_signup"
                        android:layout_width="match_parent"
                        android:layout_height="46dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginLeft="@dimen/margin_10"
                        android:layout_marginTop="30dp"
                        android:layout_marginRight="@dimen/margin_10"
                        android:layout_marginBottom="@dimen/margin_20"
                        android:background="@color/colorPrimary"
                        android:text="Create An Account"
                        android:textAllCaps="false"
                        android:textColor="@android:color/white" />


                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="By clicking Create An Account you agree to our " />

                <TextView
                    android:id="@+id/txtTermService"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:onClick="onClick"
                    android:text="Term of Services"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <ir.alirezabdn.wp7progress.WP10ProgressBar
            android:id="@+id/wp7progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            app:indicatorColor="@color/colorPrimary"
            app:indicatorHeight="7"
            app:indicatorRadius="5"
            app:interval="100" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>