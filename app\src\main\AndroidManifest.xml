<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.videostatus">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_INTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <uses-permission
        android:name="android.permission.MANAGE_DOCUMENTS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.RECORD_VIDEO" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <uses-feature
        android:name="android.hardware.wifi"
        android:required="true" />
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <application
        android:name=".Utility.MyApp"
        android:allowBackup="true"
        android:appComponentFactory="whateverString"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="false"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:appComponentFactory">

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <provider
            android:name="com.facebook.FacebookContentProvider"
            android:authorities="com.facebook.app.FacebookContentProvider2252137925009594"
            android:exported="true" />

        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />

        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>

        <activity
            android:name=".Activity.SplashActivity"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".Activity.WelcomeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".Activity.MainActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.LoginActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".Activity.Registration"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".Activity.ForgotActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".Activity.NewArrival"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".Activity.Latest"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.DownloadStatus"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.StatusDetails"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.VideoList"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".Activity.UploadVideo"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.Privacypolicy"
            android:screenOrientation="fullSensor"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.MyFavorite"
            android:screenOrientation="fullSensor"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.Profile"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.AboutUs"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.UsersListActivity"
            android:screenOrientation="fullSensor"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.CategoryListActivity"
            android:screenOrientation="fullSensor"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.EarnPointActivity"
            android:screenOrientation="fullSensor"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.PaymentDetailsActivity"
            android:screenOrientation="fullSensor"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.VideoRecord.VideoRecord"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.VideoRecord.PreviewVideo"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.VideoRecord.PostVideo"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.VideoRecord.GallaryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.VideoRecord.SoundList"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.VideoRecord.TrimVideo"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name=".Activity.HomeSingalVideo"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />

        <activity
            android:name="com.facebook.ads.AudienceNetworkActivity"
            android:configChanges="keyboardHidden|orientation|screenSize" />

        <!-- Firebase Notifications -->

        <service
            android:name=".PushNotification.NotificationExtenderExample"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE">
            <intent-filter>
                <action android:name="com.onesignal.NotificationExtender" />
            </intent-filter>
        </service>

        <service android:name=".PushNotification.MyFirebaseMessagingService">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <service android:name=".PushNotification.MyFirebaseInstanceIDService">
            <intent-filter>
                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
            </intent-filter>
        </service>
        <!-- ./Firebase Notifications -->

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="Description" />

        <receiver
            android:name=".Utility.ConnectivityReceiver"
            android:enabled="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <service
            android:name=".Utility.Upload_Service"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="startservice" />
            </intent-filter>
        </service>

    </application>

</manifest>