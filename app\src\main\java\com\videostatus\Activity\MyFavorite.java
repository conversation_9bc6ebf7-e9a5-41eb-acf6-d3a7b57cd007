package com.videostatus.Activity;

import android.app.ProgressDialog;
import android.content.Intent;
import android.graphics.PorterDuff;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.videostatus.Adapter.MyFavoriteAdapter;
import com.videostatus.Model.MyFavoriteModel.Result;
import com.videostatus.Model.MyFavoriteModel.MyFavoriteModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.Interface.ShareAll;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class MyFavorite extends AppCompatActivity implements ShareAll {

    public MyFavorite() {
    }

    RecyclerView recycler_fav;
    List<Result> favoriteList;
    MyFavoriteAdapter myFavoriteAdapter;
    String id;
    ProgressDialog progressDialog;
    PrefManager prefManager;
    TextView txt_no_record;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.my_favorite);

        Toolbar toolbar = (Toolbar) findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("My Favorite");

        toolbar.setTitleTextColor(getResources().getColor(R.color.colorPrimary));
        toolbar.getNavigationIcon().setColorFilter(getResources().getColor(R.color.colorPrimary), PorterDuff.Mode.SRC_ATOP);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowHomeEnabled(true);

        prefManager = new PrefManager(MyFavorite.this);

        progressDialog = new ProgressDialog(MyFavorite.this);
        progressDialog.setMessage("Please wait...");
        progressDialog.setCanceledOnTouchOutside(false);

        recycler_fav = (RecyclerView) findViewById(R.id.recycler_fav);
        txt_no_record = (TextView) findViewById(R.id.txt_no_record);

        if (!prefManager.getLoginId().equalsIgnoreCase("0"))
            mydowanload_video();
        else {
            startActivity(new Intent(MyFavorite.this, LoginActivity.class));
            finish();
        }

    }

    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void mydowanload_video() {

        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<MyFavoriteModel> call = bookNPlayAPI.favorite_list(prefManager.getLoginId());
        call.enqueue(new Callback<MyFavoriteModel>() {
            @Override
            public void onResponse(Call<MyFavoriteModel> call, Response<MyFavoriteModel> response) {
                progressDialog.hide();
                if (response.code() == 200) {
                    favoriteList = new ArrayList<>();
                    favoriteList = response.body().getResult();
                    Log.e("downloadList", "" + favoriteList.size());

                    if (favoriteList.size() > 0) {
                        myFavoriteAdapter = new MyFavoriteAdapter(MyFavorite.this, favoriteList, MyFavorite.this);
//                        recycler_download.setLayoutManager(new LinearLayoutManager(DownloadStatus.this, LinearLayoutManager.VERTICAL, false));
                        GridLayoutManager gridLayoutManager = new GridLayoutManager(getApplicationContext(), 2);
                        recycler_fav.setLayoutManager(gridLayoutManager);
                        recycler_fav.setItemAnimator(new DefaultItemAnimator());
                        recycler_fav.setAdapter(myFavoriteAdapter);
                        myFavoriteAdapter.notifyDataSetChanged();
                        txt_no_record.setVisibility(View.GONE);
                        recycler_fav.setVisibility(View.VISIBLE);
                    } else {
                        txt_no_record.setVisibility(View.VISIBLE);
                        txt_no_record.setText("You have not My Favorite any video yet");
                        recycler_fav.setVisibility(View.GONE);
                    }
                } else if (response.code() == 400) {
                    txt_no_record.setVisibility(View.VISIBLE);
                    txt_no_record.setText("You have not My Favorite any video yet");
                    recycler_fav.setVisibility(View.GONE);
                }
            }

            @Override
            public void onFailure(Call<MyFavoriteModel> call, Throwable t) {
                progressDialog.hide();
            }
        });
    }

    @Override
    public void WhatsappShare() {

    }
}
