<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/white</item>
        <item name="colorPrimaryDark">@color/white</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="android:windowLayoutInDisplayCutoutMode" tools:ignore="NewApi">
            shortEdges <!-- default, shortEdges, never -->
        </item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />


    <style name="ButtonWhite" parent="@android:style/Widget.Button">
        <item name="android:background">@android:color/white</item>
    </style>

    <style name="LoginSignUp" parent="Widget.Design.TabLayout">
        <item name="tabBackground">?attr/selectableItemBackground</item>
        <item name="tabIndicatorColor">@android:color/white</item>
        <item name="tabSelectedTextColor">@android:color/white</item>
        <item name="tabIndicatorHeight">3dp</item>
    </style>

    <style name="YellowCheck">
        <item name="colorControlNormal">@color/colorPrimary
        </item>
        <item name="colorControlActivated">@color/colorPrimary</item>
    </style>

    <style name="SwitchCompatTheme">
        <item name="colorControlActivated">@color/colorPrimaryDark</item>
    </style>

    <style name="AlertDialogDanger" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="colorAccent">@color/colorPrimary</item>
    </style>

    <style name="TabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">#fff</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="Progressbar_style" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="colorAccent">@color/colorPrimary</item>
    </style>



</resources>
