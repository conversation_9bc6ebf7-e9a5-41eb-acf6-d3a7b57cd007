package com.videostatus.Activity.VideoRecord;

import android.app.ProgressDialog;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.media.ThumbnailUtils;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Environment;
import android.os.IBinder;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.videostatus.Activity.MainActivity;
import com.videostatus.R;
import com.videostatus.Utility.Constant;
import com.videostatus.Utility.PrefManager;
import com.videostatus.Interface.ServiceCallback;
import com.videostatus.Utility.Upload_Service;
import com.videostatus.Utility.Utils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class PostVideo extends AppCompatActivity implements ServiceCallback, View.OnClickListener {

    ServiceCallback serviceCallback;
    String video_path;

    ImageView iv_thumbnail, iv_back;
    EditText et_description;
    LinearLayout btn_draft, btn_post;

    PrefManager prefManager;
    ProgressDialog progressDialog;

    String draft_file, duet_video_id, str_image;
    Bitmap bmThumbnail;
    File file;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.postvideo_activity);

        init();

        Intent intent = getIntent();
        if (intent != null) {
            draft_file = intent.getStringExtra("draft_file");
            duet_video_id = intent.getStringExtra("duet_video_id");
        }

        ThumbnainGenerat();

    }

    public void init() {

        prefManager = new PrefManager(PostVideo.this);

        progressDialog = new ProgressDialog(this);
        progressDialog.setMessage("Please wait");
        progressDialog.setCancelable(false);
        progressDialog.setCanceledOnTouchOutside(false);

        iv_thumbnail = findViewById(R.id.iv_thumbnail);
        iv_back = findViewById(R.id.iv_back);
        iv_back.setOnClickListener(this);

        et_description = findViewById(R.id.et_description);

        btn_draft = findViewById(R.id.btn_draft);
        btn_draft.setOnClickListener(this);

        btn_post = findViewById(R.id.btn_post);
        btn_post.setOnClickListener(this);

    }

    public void ThumbnainGenerat() {

        video_path = Constant.output_filter_file;

        bmThumbnail = ThumbnailUtils.createVideoThumbnail(video_path,
                MediaStore.Video.Thumbnails.FULL_SCREEN_KIND);

        new fileFromBitmap(bmThumbnail, getApplicationContext()).execute();

        str_image = Utils.Bitmap_to_base64(this, bmThumbnail);
        Log.e("str_image", "" + str_image);

        if (bmThumbnail != null && duet_video_id != null) {
            Bitmap duet_video_bitmap = null;
            if (duet_video_id != null) {
                duet_video_bitmap = ThumbnailUtils.createVideoThumbnail(Constant.app_showing_folder + duet_video_id + ".mp4",
                        MediaStore.Video.Thumbnails.FULL_SCREEN_KIND);
            }
            Bitmap combined = combineImages(bmThumbnail, duet_video_bitmap);
            iv_thumbnail.setImageBitmap(combined);
            String str_image1 = Utils.Bitmap_to_base64(this, combined);
            Log.e("str_image1", "" + str_image1);
            prefManager.setValue(Constant.uploading_video_thumb, str_image1);

        } else if (bmThumbnail != null) {
            iv_thumbnail.setImageBitmap(bmThumbnail);
            prefManager.setValue(Constant.uploading_video_thumb, str_image);
        }

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.iv_back:
                onBackPressed();
                break;

//            case R.id.privacy_type_layout:
//                Privacy_dialog();
//                break;

            case R.id.btn_draft:
                Save_file_in_draft();
                break;

            case R.id.btn_post:
                Start_Service();
                break;
        }
    }

    public Bitmap combineImages(Bitmap c, Bitmap s) {
        Bitmap cs = null;

        int width, height = 0;

        if (c.getWidth() > s.getWidth()) {
            width = c.getWidth() + s.getWidth();
            height = c.getHeight();
        } else {
            width = s.getWidth() + s.getWidth();
            height = c.getHeight();
        }

        cs = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);

        Canvas comboImage = new Canvas(cs);

        comboImage.drawBitmap(c, 0f, 0f, null);
        comboImage.drawBitmap(s, c.getWidth(), 0f, null);

        return cs;
    }

    // this will start the service for uploading the video into database
    public void Start_Service() {
        progressDialog.show();

        serviceCallback = this;

        Upload_Service mService = new Upload_Service(serviceCallback);
        if (!Utils.isMyServiceRunning(this, mService.getClass())) {
            Intent mServiceIntent = new Intent(this.getApplicationContext(), mService.getClass());
            mServiceIntent.setAction("startservice");
            mServiceIntent.putExtra("draft_file", draft_file);
            mServiceIntent.putExtra("duet_video_id", duet_video_id);
            mServiceIntent.putExtra("uri", "" + video_path);
            mServiceIntent.putExtra("desc", "" + et_description.getText().toString());
            mServiceIntent.putExtra("str_image", "" + file);
            mServiceIntent.putExtra("privacy_type", "");
            prefManager.setValue("desc", "" + et_description.getText().toString());
            mServiceIntent.putExtra("allow_comment", "true");
            mServiceIntent.putExtra("allow_duet", "0");

            startService(mServiceIntent);

            Intent intent = new Intent(this, Upload_Service.class);
            bindService(intent, mConnection, Context.BIND_AUTO_CREATE);

        } else {
            Toast.makeText(this, "Please wait video already in uploading progress", Toast.LENGTH_LONG).show();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        Stop_Service();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finish();
        overridePendingTransition(R.anim.in_from_left, R.anim.out_to_right);
    }

    // this is importance for binding the service to the activity
    Upload_Service mService;
    private ServiceConnection mConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName className,
                                       IBinder service) {
            Upload_Service.LocalBinder binder = (Upload_Service.LocalBinder) service;
            mService = binder.getService();
            mService.setCallbacks(PostVideo.this);
        }

        @Override
        public void onServiceDisconnected(ComponentName arg0) {
        }
    };

    @Override
    protected void onDestroy() {
        if (bmThumbnail != null) {
            bmThumbnail.recycle();
        }
        super.onDestroy();
    }

    public void Save_file_in_draft() {
        File source = new File(video_path);
        File destination = new File(Constant.draft_app_folder + Utils.getRandomString() + ".mp4");
        try {
            if (source.exists()) {

                InputStream in = new FileInputStream(source);
                OutputStream out = new FileOutputStream(destination);

                byte[] buf = new byte[1024];
                int len;

                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }

                in.close();
                out.close();

                Toast.makeText(PostVideo.this, "File saved in Draft", Toast.LENGTH_SHORT).show();
                startActivity(new Intent(PostVideo.this, MainActivity.class));

            } else {
                Toast.makeText(PostVideo.this, "File failed to saved in Draft", Toast.LENGTH_SHORT).show();
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // this function will stop the the ruuning service
    public void Stop_Service() {

        serviceCallback = this;
        Upload_Service mService = new Upload_Service(serviceCallback);
        if (Utils.isMyServiceRunning(this, mService.getClass())) {
            Intent mServiceIntent = new Intent(this.getApplicationContext(), mService.getClass());
            mServiceIntent.setAction("stopservice");
            startService(mServiceIntent);
        }
    }

    @Override
    public void ShowResponce(String responce, int code) {
        if (mConnection != null)
            unbindService(mConnection);

        Toast.makeText(PostVideo.this, responce, Toast.LENGTH_LONG).show();
        progressDialog.dismiss();

        if (code == 200) {
            startActivity(new Intent(PostVideo.this, MainActivity.class));
            finishAffinity();
        }
    }


    public class fileFromBitmap extends AsyncTask<Void, Integer, String> {

        Context context;
        Bitmap bitmap;
        String path_external = Environment.getExternalStorageDirectory() + File.separator + "temporary_file.jpg";

        public fileFromBitmap(Bitmap bitmap, Context context) {
            this.bitmap = bitmap;
            this.context = context;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
        }

        @Override
        protected String doInBackground(Void... params) {

            ByteArrayOutputStream bytes = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, bytes);
            file = new File(Environment.getExternalStorageDirectory() + File.separator + "temporary_file.jpg");
            try {
                FileOutputStream fo = new FileOutputStream(file);
                fo.write(bytes.toByteArray());
                fo.flush();
                fo.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

            return null;
        }


        @Override
        protected void onPostExecute(String s) {
            super.onPostExecute(s);
            Log.e("post", "" + s);
            file = new File(Environment.getExternalStorageDirectory() + File.separator + "temporary_file.jpg");
        }
    }
}
