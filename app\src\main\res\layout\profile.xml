<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="70dp">

            <LinearLayout
                android:id="@+id/txt_back"
                android:layout_width="30dp"
                android:layout_height="50dp"
                android:layout_alignParentLeft="true"
                android:layout_centerInParent="true"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:gravity="center">

                <TextView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:background="@drawable/ic_back"
                    android:backgroundTint="@color/colorPrimary"
                    android:gravity="center"
                    android:textColor="@color/colorPrimary" />
            </LinearLayout>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/public_bold"
                android:gravity="center"
                android:text="Profile"
                android:textColor="@color/colorPrimary"
                android:textSize="16dp" />

            <TextView
                android:id="@+id/txt_coin"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:layout_marginEnd="10dp"
                android:background="@drawable/ic_coin"
                android:fontFamily="@font/public_semibold"
                android:gravity="center"
                android:textColor="@color/font_dark"
                android:textSize="11dp" />
        </RelativeLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:gravity="center_vertical"
                    android:weightSum="1">

                    <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/iv_user_pic"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginStart="10dp"
                        android:adjustViewBounds="true"
                        android:scaleType="fitXY"
                        android:src="@color/white"
                        android:visibility="visible"
                        app:riv_border_color="@color/colorPrimary"
                        app:riv_border_width="1dip"
                        app:riv_corner_radius="60dip"
                        app:riv_oval="false"
                        app:riv_tile_mode="clamp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginStart="5dp"
                        android:layout_weight="0.65"
                        android:gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/txt_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:fontFamily="@font/public_semibold"
                            android:gravity="center_vertical"
                            android:singleLine="true"
                            android:textAllCaps="true"
                            android:textColor="@color/black"
                            android:textSize="14dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="gone"
                            android:gravity="center_vertical">

                            <TextView
                                android:layout_width="13dp"
                                android:layout_height="13dp"
                                android:background="@drawable/ic_location"
                                android:gravity="center" />

                            <TextView
                                android:id="@+id/txt_location"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:ellipsize="end"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center_vertical"
                                android:singleLine="true"
                                android:textColor="@color/gray"
                                android:textSize="@dimen/text_12" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/txt_instagram_url"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center_vertical"
                            android:textColor="@color/gray"
                            android:textSize="@dimen/text_12" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.35"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/txt_follow"
                            android:layout_width="80dp"
                            android:layout_height="25dp"
                            android:background="@drawable/round_bor_follow"
                            android:gravity="center"
                            android:text="Follow"
                            android:textColor="@color/colorPrimary" />

                    </LinearLayout>
                </LinearLayout>

                <TextView
                    android:id="@+id/txt_follower"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/margin_5"
                    android:fontFamily="@font/public_semibold"
                    android:gravity="center"
                    android:text="Followers"
                    android:textColor="@color/font_dark"
                    android:textSize="14dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_followers"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:scrollbars="none" />

                </LinearLayout>

                <TextView
                    android:id="@+id/txt_following"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/margin_5"
                    android:fontFamily="@font/public_semibold"
                    android:gravity="center"
                    android:text="Following"
                    android:textColor="@color/font_dark"
                    android:textSize="14dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_following"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:scrollbars="none" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/margin_10"
                    android:gravity="center">

                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/tabLayout"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:background="@drawable/tab_layout_background"
                        app:tabBackground="@drawable/tab_layout_selector"
                        app:tabIndicatorHeight="0dp"
                        app:tabMode="scrollable"
                        app:tabPaddingEnd="16dp"
                        app:tabPaddingStart="16dp"
                        app:tabRippleColor="@null"
                        app:tabSelectedTextColor="@color/white"
                        app:tabTextAppearance="@style/TabTextAppearance" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_uploaded"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:nestedScrollingEnabled="true"
                        android:scrollbars="none"
                        android:visibility="visible" />

                    <TextView
                        android:id="@+id/txt_no_record"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:visibility="gone"
                        android:text="Whoop!!!\n No Record Found"
                        android:textColor="@color/colorPrimary"
                        android:textSize="20dp" />

                    <LinearLayout
                        android:id="@+id/ly_second"
                        android:layout_width="match_parent"
                        android:orientation="vertical"
                        android:visibility="gone"
                        android:layout_height="match_parent">


                        <TextView
                            android:id="@+id/txt_like"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/margin_5"
                            android:fontFamily="@font/public_semibold"
                            android:gravity="center"
                            android:text="You Liked"
                            android:textColor="@color/font_dark"
                            android:textSize="14dp"/>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycler_like"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:nestedScrollingEnabled="true"
                            android:scrollbars="none"
                            android:visibility="visible" />

                        <TextView
                            android:id="@+id/txt_comment"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/margin_5"
                            android:fontFamily="@font/public_semibold"
                            android:gravity="center"
                            android:text="You Comments"
                            android:textColor="@color/font_dark"
                            android:textSize="14dp"/>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycler_comment"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:nestedScrollingEnabled="true"
                            android:scrollbars="none"
                            android:visibility="visible" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>