package com.videostatus.Fragment;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.videostatus.Adapter.Comments_Adapter;
import com.videostatus.Model.CommentModel.CommentModel;
import com.videostatus.Model.CommentModel.Result;
import com.videostatus.Model.SuccessModel.SuccessModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;
import java.util.ArrayList;
import java.util.List;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class Comment extends BottomSheetDialogFragment {

    View view;
    Context context;

    RecyclerView recyclerView;
    Comments_Adapter adapter;
    List<Result> CommentList;

    String video_id;
    String user_id;

    EditText message_edit;
    ImageButton send_btn;
    ProgressBar send_progress;
    TextView comment_count_txt;

    FrameLayout comment_screen;
    PrefManager prefManager;

    private BottomSheetBehavior mBehavior;
    public static int comment_count = 0;

    public static Comment newInstance() {
        return new Comment();
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        View view = View.inflate(getContext(), R.layout.fragment_comment, null);

        context = getContext();

        prefManager = new PrefManager(getActivity());

        comment_screen = view.findViewById(R.id.comment_screen);
        comment_screen.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        view.findViewById(R.id.iv_back).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        Bundle bundle = getArguments();
        if (bundle != null) {
            video_id = bundle.getString("video_id");
            user_id = bundle.getString("user_id");
            Log.e("VID==>" + video_id, "user_id==>" + user_id);
        }

        comment_count_txt = view.findViewById(R.id.comment_count);

        recyclerView = view.findViewById(R.id.recylerview);
        LinearLayoutManager layoutManager = new LinearLayoutManager(context);
        recyclerView.setLayoutManager(layoutManager);
        recyclerView.setHasFixedSize(false);

        message_edit = view.findViewById(R.id.message_edit);

        send_progress = view.findViewById(R.id.send_progress);
        send_btn = view.findViewById(R.id.send_btn);
        send_btn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String message = message_edit.getText().toString();
                if (!TextUtils.isEmpty(message)) {
                    if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                        Send_Comments(video_id, message);
                        message_edit.setText(null);
                        send_progress.setVisibility(View.VISIBLE);
                        send_btn.setVisibility(View.VISIBLE);
                    } else {
                        Toast.makeText(context, "Please Login into the app", Toast.LENGTH_SHORT).show();
                    }
                }
            }
        });

        Get_All_Comments();

        dialog.setContentView(view);
        mBehavior = BottomSheetBehavior.from((View) view.getParent());
        return dialog;

    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    // this funtion will get all the comments against post
    public void Get_All_Comments() {

        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<CommentModel> call = bookNPlayAPI.view_comment(video_id);
        call.enqueue(new Callback<CommentModel>() {
            @Override
            public void onResponse(Call<CommentModel> call, Response<CommentModel> response) {
                if (response.code() == 200) {

                    Log.e("Related_Item", "" + response);

                    CommentList = new ArrayList<>();
                    CommentList = response.body().getResult();
                    Log.e("CommentList", "" + CommentList.size());
                    comment_count_txt.setText(CommentList.size() + " comments");

                    adapter = new Comments_Adapter(getActivity(), CommentList);
                    recyclerView.setHasFixedSize(true);
                    RecyclerView.LayoutManager mLayoutManager3 = new LinearLayoutManager(getActivity(),
                            LinearLayoutManager.VERTICAL, false);
                    recyclerView.setLayoutManager(mLayoutManager3);
                    recyclerView.setItemAnimator(new DefaultItemAnimator());
                    recyclerView.setAdapter(adapter);
                    adapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<CommentModel> call, Throwable t) {
            }
        });
    }

    // this function will call an api to upload your comment
    public void Send_Comments(String video_id, final String comment) {

        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.add_comment(video_id,
                prefManager.getLoginId(), comment);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {

                Log.e("Add Comments", "" + response.body().getMessage());
                Toast.makeText(getActivity(), "" + response.body().getMessage(), Toast.LENGTH_SHORT).show();
                send_progress.setVisibility(View.GONE);
//                send_btn.setVisibility(View.VISIBLE);
                Get_All_Comments();
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
                send_progress.setVisibility(View.GONE);
            }
        });

//        Functions.Call_Api_For_Send_Comment(getActivity(), video_id, comment, new API_CallBack() {
//            @Override
//            public void ArrayData(ArrayList arrayList) {
//
//                ArrayList<Comment_Get_Set> arrayList1 = arrayList;
//                for (Comment_Get_Set item : arrayList1) {
//                    data_list.add(0, item);
//                    comment_count++;
//
//                    SendPushNotification(getActivity(), user_id, comment);
//
//                    comment_count_txt.setText(comment_count + " comments");
//
//                    if (fragment_data_send != null)
//                        fragment_data_send.onDataSent("" + comment_count);
//
//                }
//                adapter.notifyDataSetChanged();
//                send_progress.setVisibility(View.GONE);
//                send_btn.setVisibility(View.VISIBLE);
//            }
//
//            @Override
//            public void OnSuccess(String responce) {
//
//            }
//
//            @Override
//            public void OnFail(String responce) {
//
//            }
//        });

    }

    public void SendPushNotification(Activity activity, String user_id, String comment) {

//        JSONObject notimap = new JSONObject();
//        try {
//            notimap.put("title", Variables.sharedPreferences.getString(Variables.u_name, "") + " Comment on your video");
//            notimap.put("message", comment);
//            notimap.put("icon", Variables.sharedPreferences.getString(Variables.u_pic, ""));
//            notimap.put("senderid", Variables.sharedPreferences.getString(Variables.u_id, ""));
//            notimap.put("receiverid", user_id);
//            notimap.put("action_type", "comment");
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//
//        ApiRequest.Call_Api(context, Variables.sendPushNotification, notimap, null);
//
    }

}
