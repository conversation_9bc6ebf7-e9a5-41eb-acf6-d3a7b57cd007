<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/ly_main"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible"
        tools:ignore="MissingConstraints">

        <androidx.cardview.widget.CardView
            android:layout_width="120dp"
            android:layout_height="70dp"
            android:layout_margin="5dp"
            android:orientation="vertical"
            app:cardCornerRadius="8dp"
            tools:ignore="MissingConstraints">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="1.0">

                <ImageView
                    android:id="@+id/iv_thumb"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center"
                    android:scaleType="fitXY" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="3dp"
                    android:fontFamily="@font/public_medium"
                    android:gravity="center"
                    android:singleLine="true"
                    android:textColor="@color/font_dark"
                    android:textIsSelectable="true"
                    android:textSize="@dimen/text_14" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>