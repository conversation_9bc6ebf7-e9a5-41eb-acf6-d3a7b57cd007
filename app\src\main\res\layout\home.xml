<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/lib/com.google.ads"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical">

                        <androidx.viewpager.widget.ViewPager
                            android:id="@+id/viewPager"
                            android:layout_width="match_parent"
                            android:layout_height="300dp"
                            android:clipToPadding="false" />

                        <com.viewpagerindicator.LinePageIndicator
                            android:id="@+id/line_indictor"
                            android:layout_width="match_parent"
                            android:layout_height="25dp"
                            app:selectedColor="@color/colorAccent"
                            app:strokeWidth="2dp"
                            app:unselectedColor="@color/gray" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:weightSum="1.0">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="0.7"
                            android:text="@string/top_user"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center_vertical"
                            android:textColor="@color/colorPrimary"
                            android:textSize="@dimen/text_14" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="0.3"
                            android:gravity="center_vertical|end">

                            <TextView
                                android:id="@+id/txt_viewall_user"
                                android:layout_width="70dp"
                                android:layout_height="28dp"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center"
                                android:text="@string/view_all"
                                android:textColor="@color/font_dark"
                                android:textSize="@dimen/text_14" />
                        </LinearLayout>
                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_artist"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="3dp"
                        android:visibility="visible" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/ly_feature_image_view_all"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:weightSum="1.0">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.7"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center_vertical"
                                android:text="@string/latest_status"
                                android:textColor="@color/colorPrimary"
                                android:textSize="@dimen/text_14" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.3"
                                android:gravity="center_vertical|end">

                                <TextView
                                    android:id="@+id/txt_view_all_latest"
                                    android:layout_width="70dp"
                                    android:layout_height="28dp"
                                    android:fontFamily="@font/public_medium"
                                    android:gravity="center"
                                    android:text="@string/view_all"
                                    android:textColor="@color/font_dark"
                                    android:textSize="@dimen/text_14" />
                            </LinearLayout>
                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycler_latest"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:nestedScrollingEnabled="true"
                            android:scrollbars="none"
                            android:visibility="visible" />
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:visibility="gone"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:weightSum="1.0">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="0.7"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center_vertical"
                            android:text="@string/category"
                            android:textColor="@color/colorPrimary"
                            android:textSize="@dimen/text_14" />


                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="0.3"
                            android:gravity="center_vertical|end">

                            <TextView
                                android:id="@+id/txt_viewall_category"
                                android:layout_width="70dp"
                                android:layout_height="28dp"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center"
                                android:text="@string/view_all"
                                android:textColor="@color/font_dark"
                                android:textSize="@dimen/text_14" />
                        </LinearLayout>

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_category"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="visible" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/ly_popular_viewall"
                            android:layout_width="match_parent"
                            android:layout_height="40dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:weightSum="1.0">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.7"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center_vertical"
                                android:text="@string/new_arrival"
                                android:textColor="@color/colorPrimary"
                                android:textSize="@dimen/text_14" />

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_weight="0.3"
                                android:gravity="center_vertical|end">

                                <TextView
                                    android:id="@+id/txt_view_all_newarrival"
                                    android:layout_width="70dp"
                                    android:layout_height="28dp"
                                    android:fontFamily="@font/public_medium"
                                    android:gravity="center"
                                    android:text="@string/view_all"
                                    android:textColor="@color/font_dark"
                                    android:textSize="@dimen/text_14" />
                            </LinearLayout>

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycler_popular"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:nestedScrollingEnabled="true"
                            android:scrollbars="none"
                            android:visibility="visible" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>