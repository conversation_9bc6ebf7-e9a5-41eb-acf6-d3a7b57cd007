<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    android:background="@color/white"
                    app:popupTheme="@style/AppTheme.PopupOverlay"
                    app:titleTextColor="@color/colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:id="@+id/ly_back"
                            android:layout_width="30dp"
                            android:layout_height="match_parent">

                            <TextView
                                android:layout_width="20dp"
                                android:layout_height="20dp"
                                android:layout_gravity="center_vertical"
                                android:background="@drawable/ic_back"
                                android:backgroundTint="@color/colorPrimary"
                                android:gravity="center"
                                android:textSize="16dp" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/toolbar_title"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="10dp"
                            android:gravity="center"
                            android:text="@string/earn_point"
                            android:textColor="@color/colorPrimary"
                            android:textSize="16dp" />
                    </LinearLayout>
                </androidx.appcompat.widget.Toolbar>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:visibility="visible"
                tools:ignore="MissingConstraints">

                <androidx.cardview.widget.CardView
                    android:id="@+id/cv_10"
                    android:layout_width="match_parent"
                    android:layout_height="160dp"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    app:cardCornerRadius="8dp"
                    tools:ignore="MissingConstraints">

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:layout_centerVertical="true"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center"
                            android:singleLine="true"
                            android:text="Watch Earn"
                            android:textColor="@color/font_dark"
                            android:textSize="14dp" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:layout_marginTop="5dp"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/iv_thumb"
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:fontFamily="@font/public_regular"
                                android:gravity="center"
                                android:background="@drawable/circle"
                                android:text="10"
                                android:textStyle="bold"
                                android:textSize="30dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/title"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerHorizontal="true"
                                android:layout_centerVertical="true"
                                android:layout_marginTop="10dp"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center"
                                android:singleLine="true"
                                android:text="Admob"
                                android:textColor="@color/colorPrimary"
                                android:textSize="14dp" />


                        </LinearLayout>

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/cv_20"
                    android:layout_width="match_parent"
                    android:layout_height="160dp"
                    android:layout_margin="10dp"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    app:cardCornerRadius="8dp"
                    tools:ignore="MissingConstraints">

                    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:layout_centerVertical="true"
                            android:fontFamily="@font/public_medium"
                            android:gravity="center"
                            android:singleLine="true"
                            android:text="Watch Earn"
                            android:textColor="@color/font_dark"
                            android:textSize="14dp" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:layout_marginTop="5dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="80dp"
                                android:layout_height="80dp"
                                android:fontFamily="@font/public_regular"
                                android:gravity="center"
                                android:background="@drawable/circle"
                                android:text="20"
                                android:textStyle="bold"
                                android:textSize="30dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerHorizontal="true"
                                android:layout_centerVertical="true"
                                android:layout_marginTop="10dp"
                                android:fontFamily="@font/public_medium"
                                android:gravity="center"
                                android:singleLine="true"
                                android:text="Facebook"
                                android:textColor="@color/colorPrimary"
                                android:textSize="14dp" />


                        </LinearLayout>

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>