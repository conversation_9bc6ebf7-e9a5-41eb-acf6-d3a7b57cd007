<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config>
        <device id="pixel_xl" />
        <device id="4in WVGA (Nexus S)" />
        <device id="3.3in WQVGA" />
        <device id="3.7in WVGA (Nexus One)" />
        <device id="pixel" />
        <device id="Nexus 7" />
        <device id="Nexus 6" />
        <device id="3.7 FWVGA slider" />
        <device id="4.7in WXGA" />
        <target>android-28</target>
      </config>
    </shared>
  </component>
  <component name="AndroidLogFilters">
    <option name="TOOL_WINDOW_LOG_LEVEL" value="error" />
    <option name="TOOL_WINDOW_CONFIGURED_FILTER" value="No Filters" />
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="28c33b0d-752a-4751-8521-e464080c8c53" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/.gradle/6.1.1/executionHistory/executionHistory.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/6.1.1/executionHistory/executionHistory.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/6.1.1/executionHistory/executionHistory.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/6.1.1/executionHistory/executionHistory.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/6.1.1/fileHashes/fileHashes.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/6.1.1/fileHashes/fileHashes.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/6.1.1/fileHashes/fileHashes.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/6.1.1/fileHashes/fileHashes.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/6.1.1/fileHashes/resourceHashesCache.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/6.1.1/fileHashes/resourceHashesCache.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/6.1.1/javaCompile/classAnalysis.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/6.1.1/javaCompile/classAnalysis.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/6.1.1/javaCompile/jarAnalysis.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/6.1.1/javaCompile/jarAnalysis.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/6.1.1/javaCompile/javaCompile.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/6.1.1/javaCompile/javaCompile.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/6.1.1/javaCompile/taskHistory.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/6.1.1/javaCompile/taskHistory.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/buildOutputCleanup/buildOutputCleanup.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/buildOutputCleanup/buildOutputCleanup.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/buildOutputCleanup/cache.properties" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/buildOutputCleanup/cache.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/buildOutputCleanup/outputFiles.bin" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/checksums/checksums.lock" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/checksums/checksums.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gradle/checksums/sha1-checksums.bin" beforeDir="false" afterPath="$PROJECT_DIR$/.gradle/checksums/sha1-checksums.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/caches/build_file_checksums.ser" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/caches/build_file_checksums.ser" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/gradle.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/gradle.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/runConfigurations.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/runConfigurations.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/app/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/videostatus/Activity/VideoRecord/GallaryActivity.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/videostatus/Activity/VideoRecord/GallaryActivity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/videostatus/Activity/VideoRecord/PreviewVideo.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/videostatus/Activity/VideoRecord/PreviewVideo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/videostatus/Utility/Utils.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/videostatus/Utility/Utils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/java/com/videostatus/WebService/BaseURL.java" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/java/com/videostatus/WebService/BaseURL.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/layout/previewvideo.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/layout/previewvideo.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/src/main/res/values/ads.xml" beforeDir="false" afterPath="$PROJECT_DIR$/app/src/main/res/values/ads.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/gradle.properties" beforeDir="false" afterPath="$PROJECT_DIR$/gradle.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/gradle/wrapper/gradle-wrapper.properties" beforeDir="false" afterPath="$PROJECT_DIR$/gradle/wrapper/gradle-wrapper.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/gradlew" beforeDir="false" afterPath="$PROJECT_DIR$/gradlew" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/local.properties" beforeDir="false" afterPath="$PROJECT_DIR$/local.properties" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="VideoStatus" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="VideoStatus" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="VideoStatus" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
                <item name="android" type="c8890929:TasksNode$1" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="resourceFile" />
        <option value="Class" />
        <option value="layoutResourceFile" />
        <option value="Interface" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HistoryComponent">
    <option name="urls">
      <array />
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="1cZcjmKMEU6CEv0wifr8ODQ1Klj" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Gradle.Upgrade Gradle wrapper.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;android.gradle.sync.needed&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/StudioProjects/MainFiles/AppCode/VideoStatus&quot;,
    &quot;show.migrate.to.gradle.popup&quot;: &quot;false&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.videostatus.Adapter" />
      <recent name="com.videostatus.Activity" />
      <recent name="com.videostatus.Utility" />
      <recent name="com.videostatus.Fragment" />
      <recent name="com.videostatus.Model.SuccessModel" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/app/src/main/res/drawable" />
      <recent name="$PROJECT_DIR$/app/src/main/res/layout" />
      <recent name="$PROJECT_DIR$/app/src/main/java/com/videostatus/Adapter" />
      <recent name="$PROJECT_DIR$/app/src/main/java/com/videostatus/Model" />
      <recent name="$PROJECT_DIR$/app/src/main/java/com/videostatus" />
    </key>
  </component>
  <component name="RunManager" selected="Android App.app">
    <configuration default="true" type="AndroidJUnit" factoryName="Android JUnit">
      <option name="TEST_OBJECT" value="class" />
      <option name="WORKING_DIRECTORY" value="$MODULE_DIR$" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Applet">
      <option name="POLICY_FILE" value="$APPLICATION_HOME_DIR$/bin/appletviewer.policy" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Sample Java Methods" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="VideoStatus [signingReport]" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value="signingReport" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <option name="TEST_OBJECT" value="class" />
      <option name="WORKING_DIRECTORY" value="$MODULE_DIR$" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="TestNG">
      <option name="TEST_OBJECT" value="CLASS" />
      <option name="WORKING_DIRECTORY" value="$MODULE_DIR$" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="#org.jetbrains.idea.devkit.run.PluginConfigurationType">
      <module name="" />
      <option name="VM_PARAMETERS" value="-Xmx512m -Xms256m -XX:MaxPermSize=250m -ea" />
      <option name="PROGRAM_PARAMETERS" />
      <predefined_log_file enabled="true" id="idea.log" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Gradle.VideoStatus [signingReport]" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="28c33b0d-752a-4751-8521-e464080c8c53" name="Default Changelist" comment="" />
      <created>1550552377071</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1550552377071</updated>
    </task>
    <servers />
  </component>
  <component name="TodoView">
    <todo-panel id="selected-file">
      <is-autoscroll-to-source value="true" />
    </todo-panel>
    <todo-panel id="all">
      <are-packages-shown value="true" />
      <is-autoscroll-to-source value="true" />
    </todo-panel>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="WindowStateProjectService">
    <state x="768" y="105" width="374" height="626" key="#com.intellij.ide.util.MemberChooser" timestamp="1600338473263">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state x="768" y="105" width="374" height="626" key="#com.intellij.ide.util.MemberChooser/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1600338473263" />
    <state x="693" y="229" width="524" height="502" key="#com.intellij.refactoring.safeDelete.UnsafeUsagesDialog" timestamp="1600168584695">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state x="693" y="229" width="524" height="502" key="#com.intellij.refactoring.safeDelete.UnsafeUsagesDialog/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1600168584695" />
    <state x="543" y="247" width="824" height="484" key="AndroidCreateResourceFileDialog" timestamp="1600142931976">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state x="543" y="247" width="824" height="484" key="AndroidCreateResourceFileDialog/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1600142931976" />
    <state x="805" y="266" key="AndroidProcessChooserDialog" timestamp="1599651706277">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state x="805" y="266" key="AndroidProcessChooserDialog/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1599651706277" />
    <state x="738" y="209" width="472" height="484" key="FileChooserDialogImpl" timestamp="1600337740894">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state x="738" y="209" width="472" height="484" key="FileChooserDialogImpl/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1600337740894" />
    <state width="1874" height="346" key="GridCell.Tab.0.bottom" timestamp="1600751944623">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state width="1320" height="321" key="GridCell.Tab.0.bottom/1920.0.1366.768/0.27.1366.704@0.27.1366.704" timestamp="1600334805697" />
    <state width="1874" height="346" key="GridCell.Tab.0.bottom/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1600751944623" />
    <state width="1874" height="346" key="GridCell.Tab.0.center" timestamp="1600751944622">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state width="1320" height="321" key="GridCell.Tab.0.center/1920.0.1366.768/0.27.1366.704@0.27.1366.704" timestamp="1600334805697" />
    <state width="1874" height="346" key="GridCell.Tab.0.center/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1600751944622" />
    <state width="1874" height="346" key="GridCell.Tab.0.left" timestamp="1600751944622">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state width="1320" height="321" key="GridCell.Tab.0.left/1920.0.1366.768/0.27.1366.704@0.27.1366.704" timestamp="1600334805697" />
    <state width="1874" height="346" key="GridCell.Tab.0.left/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1600751944622" />
    <state width="1874" height="346" key="GridCell.Tab.0.right" timestamp="1600751944623">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state width="1320" height="321" key="GridCell.Tab.0.right/1920.0.1366.768/0.27.1366.704@0.27.1366.704" timestamp="1600334805697" />
    <state width="1874" height="346" key="GridCell.Tab.0.right/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1600751944623" />
    <state x="689" y="205" width="618" height="524" key="StructurePopup" timestamp="1600401341678">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state x="689" y="205" width="618" height="524" key="StructurePopup/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1600401341678" />
    <state x="732" y="293" width="446" height="438" key="chooseDestDirectoryDialog" timestamp="1599451990734">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state x="732" y="293" width="446" height="438" key="chooseDestDirectoryDialog/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1599451990734" />
    <state x="612" y="209" width="696" height="522" key="find.popup" timestamp="1600168762034">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state x="612" y="209" width="696" height="522" key="find.popup/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1600168762034" />
    <state x="623" y="52" width="672" height="678" key="search.everywhere.popup" timestamp="1600417134381">
      <screen x="0" y="27" width="1920" height="704" />
    </state>
    <state x="623" y="52" width="672" height="678" key="search.everywhere.popup/1920.0.1366.768/0.27.1920.704@0.27.1920.704" timestamp="1600417134381" />
  </component>
  <component name="masterDetails">
    <states>
      <state key="ProjectJDKs.UI">
        <settings>
          <last-edited>1.8</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
    </states>
  </component>
</project>