<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/white"
            app:popupTheme="@style/AppTheme.PopupOverlay"
            app:titleTextColor="@color/colorPrimary">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/ly_back"
                    android:layout_width="30dp"
                    android:layout_height="match_parent">

                    <TextView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_back"
                        android:backgroundTint="@color/colorPrimary"
                        android:gravity="center"
                        android:textSize="16dp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/toolbar_title"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="10dp"
                    android:gravity="center"
                    android:text="@string/about_us"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16dp" />
            </LinearLayout>
        </androidx.appcompat.widget.Toolbar>

    </LinearLayout>


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:orientation="vertical"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                tools:ignore="MissingConstraints">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.2"
                        android:gravity="center">

                        <ImageView
                            android:id="@+id/iv_app_icon"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:background="@mipmap/ic_launcher_round" />
                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="40dp"
                        android:background="@color/grey_10" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.8"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/txt_app_name"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="10dp"
                            android:gravity="center_vertical"
                            android:text="@string/app_name"
                            android:textColor="@color/font_dark"
                            android:textSize="20dp"
                            android:textStyle="bold" />
                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:orientation="vertical"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                tools:ignore="MissingConstraints">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.2"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:src="@drawable/ic_company" />
                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="40dp"
                        android:background="@color/grey_10" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.8"
                        android:gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:gravity="center_vertical"
                            android:text="@string/Company"
                            android:textColor="@color/font_dark"
                            android:textSize="16dp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/txt_company"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:gravity="center_vertical"
                            android:textColor="@color/font_dark"
                            android:textSize="16dp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:orientation="vertical"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                tools:ignore="MissingConstraints">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.2"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:src="@drawable/ic_mail" />
                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="40dp"
                        android:background="@color/grey_10" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.8"
                        android:gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:gravity="center_vertical"
                            android:text="@string/email"
                            android:textColor="@color/font_dark"
                            android:textSize="16dp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/txt_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:gravity="center_vertical"
                            android:textColor="@color/font_dark"
                            android:textSize="16dp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:orientation="vertical"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                tools:ignore="MissingConstraints">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.2"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:src="@drawable/ic_internet" />
                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="40dp"
                        android:background="@color/grey_10" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.8"
                        android:gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:gravity="center_vertical"
                            android:text="@string/website"
                            android:textColor="@color/font_dark"
                            android:textSize="16dp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/txt_website"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:gravity="center_vertical"
                            android:textColor="@color/font_dark"
                            android:textSize="16dp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:orientation="vertical"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                tools:ignore="MissingConstraints">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.2"
                        android:gravity="center">

                        <ImageView
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:src="@drawable/ic_telephone" />
                    </LinearLayout>

                    <View
                        android:layout_width="1dp"
                        android:layout_height="40dp"
                        android:background="@color/grey_10" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.8"
                        android:gravity="center_vertical"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:gravity="center_vertical"
                            android:text="@string/Contact"
                            android:textColor="@color/font_dark"
                            android:textSize="16dp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/txt_mobile"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:gravity="center_vertical"
                            android:textColor="@color/font_dark"
                            android:textSize="16dp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <RelativeLayout
                android:id="@+id/rl_adView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:background="@color/white"
                android:visibility="visible"></RelativeLayout>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:orientation="vertical"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="5dp"
                tools:ignore="MissingConstraints">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:gravity="center_vertical"
                        android:text="@string/about_us"
                        android:textColor="@color/font_dark"
                        android:textSize="16dp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/txt_about_us"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="5dp"
                        android:textColor="@color/font_dark"
                        android:textSize="16dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>