<component name="libraryTable">
  <library name="Gradle: com.github.bumptech.glide:gifdecoder:4.9.0@aar">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/0fbf03a254857ac4b2c56437dbc4ab7f/jetified-gifdecoder-4.9.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/0fbf03a254857ac4b2c56437dbc4ab7f/jetified-gifdecoder-4.9.0/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/0fbf03a254857ac4b2c56437dbc4ab7f/jetified-gifdecoder-4.9.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/0fbf03a254857ac4b2c56437dbc4ab7f/jetified-gifdecoder-4.9.0/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.bumptech.glide/gifdecoder/4.9.0/5a535e1ca9189de869e65c35ebf5f76b565e6c07/gifdecoder-4.9.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.github.bumptech.glide/gifdecoder/4.9.0/5b2b5c315c48fdfef00435450d8366e498dba7de/gifdecoder-4.9.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>