package com.videostatus.Activity;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.videostatus.Adapter.LatestAdapter;
import com.videostatus.Model.VideoModel.Result;
import com.videostatus.Model.VideoModel.VideoModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.Interface.ShareAll;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.util.ArrayList;
import java.util.List;

import ir.alirezabdn.wp7progress.WP10ProgressBar;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class Latest extends AppCompatActivity implements ShareAll {

    public Latest() {
    }

    WP10ProgressBar progressBar;
    RecyclerView recycler_latest;
    List<Result> LatestList;
    LatestAdapter latestAdapter;

    PrefManager prefManager;
    Toolbar toolbar;
    LinearLayout ly_back;
    TextView toolbar_title;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.latest);

        Toolbar toolbar = (Toolbar) findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        PrefManager.forceRTLIfSupported(getWindow(), Latest.this);
        prefManager = new PrefManager(Latest.this);

        toolbar_title = findViewById(R.id.toolbar_title);
        toolbar_title.setText(getResources().getString(R.string.new_arrival));
        progressBar = findViewById(R.id.wp7progressBar);
        recycler_latest = findViewById(R.id.recycler_latest);

        ly_back = findViewById(R.id.ly_back);
        ly_back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        Latest();

    }

    private void Latest() {

        progressBar.showProgressBar();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<VideoModel> call = bookNPlayAPI.latest_video();
        call.enqueue(new Callback<VideoModel>() {
            @Override
            public void onResponse(Call<VideoModel> call, Response<VideoModel> response) {
                progressBar.hideProgressBar();
                if (response.code() == 200) {
                    LatestList = new ArrayList<>();
                    LatestList = response.body().getResult();
                    Log.e("LatestVideoList", "" + LatestList.size());

                    latestAdapter = new LatestAdapter(Latest.this, LatestList, "");
                    GridLayoutManager gridLayoutManager = new GridLayoutManager(getApplicationContext(), 2);
                    recycler_latest.setLayoutManager(gridLayoutManager);
                    recycler_latest.setItemAnimator(new DefaultItemAnimator());
                    recycler_latest.setAdapter(latestAdapter);
                    latestAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<VideoModel> call, Throwable t) {
                progressBar.hideProgressBar();
            }
        });
    }
    @Override
    public void WhatsappShare() {

    }
}
