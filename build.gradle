// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    
    repositories {
        google()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.0.1'
        classpath 'com.google.gms:google-services:4.0.1'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        maven { url  "https://giphy.bintray.com/giphy-sdk" }
        maven { url 'https://jitpack.io' }

        flatDir{
            dirs 'libs'
        }

        maven {
            url = 'https://oss.sonatype.org/content/repositories/snapshots/'
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
