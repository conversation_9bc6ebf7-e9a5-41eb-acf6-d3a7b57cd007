<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="0dp"
        app:cardBackgroundColor="@color/black">

        <RelativeLayout
            android:id="@+id/mainlayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <FrameLayout
                android:id="@+id/fullscreen_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.google.android.exoplayer2.ui.PlayerView
                    android:id="@+id/playerview"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/black"
                    app:controller_layout_id="@layout/item_player_controler"
                    app:hide_during_ads="true"
                    app:resize_mode="fixed_width"
                    app:show_timeout="1000"
                    app:shutter_background_color="@color/black" />
            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/margin_50"
                android:layout_marginTop="30dp"
                android:gravity="center_vertical">

                <RelativeLayout
                    android:layout_width="90dp"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical|right">

                    <TextView
                        android:id="@+id/txt_following"
                        android:layout_width="wrap_content"
                        android:layout_height="25dp"
                        android:layout_marginStart="10dp"
                        android:fontFamily="@font/public_semibold"
                        android:gravity="center|right"
                        android:text="Following"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                    <TextView
                        android:id="@+id/txt_following_select"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:layout_alignParentEnd="true"
                        android:background="@drawable/round_bg"
                        android:textColor="@color/black"
                        android:visibility="invisible"
                        android:textSize="14dp" />

                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="90dp"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical|right">

                    <TextView
                        android:id="@+id/txt_related"
                        android:layout_width="wrap_content"
                        android:layout_height="25dp"
                        android:layout_marginStart="15dp"
                        android:fontFamily="@font/public_semibold"
                        android:gravity="center"
                        android:text="Related"
                        android:textColor="@color/white"
                        android:textSize="14dp" />

                    <TextView
                        android:id="@+id/txt_related_select"
                        android:layout_width="8dp"
                        android:layout_height="8dp"
                        android:layout_alignParentEnd="true"
                        android:layout_marginEnd="8dp"
                        android:background="@drawable/round_bg"
                        android:textColor="@color/black"
                        android:visibility="invisible"
                        android:textSize="14dp" />
                </RelativeLayout>
            </LinearLayout>


            <LinearLayout
                android:id="@+id/side_menu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/ly_bottom"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="30dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ly_like_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_like_image"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:background="@drawable/ic_like"
                        android:scaleType="fitCenter" />

                    <TextView
                        android:id="@+id/txt_like"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="1"
                        android:textColor="@color/white"
                        android:textSize="12dp"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_comment_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_comment_image"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/ic_comment" />

                    <TextView
                        android:id="@+id/txt_comment"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="3"
                        android:textColor="@color/white"
                        android:textSize="12dp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_shared_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/ic_share" />

                    <TextView
                        android:id="@+id/txt_share"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="3"
                        android:textColor="@color/white"
                        android:textSize="12dp"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ly_sound_image_layout"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/ic_disk_bg"
                    android:gravity="center"
                    android:orientation="vertical">

                    <de.hdodenhof.circleimageview.CircleImageView
                        android:id="@+id/iv_sound_image"
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:background="@drawable/ic_music" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ly_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:background="@drawable/ic_bottom_bg"
                android:minHeight="110dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/margin_15"
                    android:minHeight="60dp"
                    android:weightSum="1.0">

                    <TextView
                        android:id="@+id/txt_username"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="35dp"
                        android:layout_weight="0.6"
                        android:maxLines="3"
                        android:shadowColor="@color/black"
                        android:text="Tere Ghar par sanam"
                        android:textColor="@color/white"
                        android:textSize="12dp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.4"
                        android:gravity="center">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/txt_whatsapp"
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:background="@drawable/ic_whatsapp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/txt_insta"
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:background="@drawable/ic_instagram" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/txt_facebook"
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:background="@drawable/ic_facebook" />
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <TextView
                    android:id="@+id/txt_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/margin_15"
                    android:layout_marginTop="@dimen/margin_5"
                    android:ellipsize="end"
                    android:maxLines="3"
                    android:shadowColor="@color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="2"
                    android:textColor="@color/white"
                    android:textSize="13dp"
                    android:visibility="gone" />

                <LinearLayout
                    android:id="@+id/ly_sound"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginLeft="@dimen/margin_15"
                    android:layout_marginTop="@dimen/margin_5"
                    android:layout_marginBottom="5dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:background="@drawable/ic_music"
                        android:backgroundTint="@color/white" />

                    <TextView
                        android:id="@+id/txt_sound_name"
                        android:layout_width="140dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="7dp"
                        android:ellipsize="marquee"
                        android:marqueeRepeatLimit="marquee_forever"
                        android:scrollHorizontally="true"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="3"
                        android:singleLine="true"
                        android:textColor="@color/white"
                        android:textSize="13dp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

            <RelativeLayout
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_above="@+id/ly_bottom"
                android:layout_marginLeft="@dimen/margin_10"
                android:layout_marginBottom="-30dp">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/iv_user_pic"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:layout_marginLeft="@dimen/margin_10"
                    android:layout_marginBottom="-30dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/profile_bg"
                    app:civ_border_color="@color/white"
                    app:civ_border_width="3dp" />


                <ImageView
                    android:id="@+id/iv_add_follow"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginBottom="7dp"
                    android:background="@drawable/ic_add_follow" />
            </RelativeLayout>

        </RelativeLayout>

    </androidx.cardview.widget.CardView>

</RelativeLayout>