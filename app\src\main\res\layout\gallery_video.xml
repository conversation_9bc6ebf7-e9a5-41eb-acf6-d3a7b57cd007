<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Activity.VideoRecord.GallaryActivity">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.exoplayer2.ui.PlayerView
            android:id="@+id/playerview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="0dp"
            android:background="@color/black"
            android:padding="0dp"
            app:controller_layout_id="@layout/item_player_controler"
            app:resize_mode="fixed_height"
            app:show_timeout="1000"
            app:shutter_background_color="@color/black" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="40dp"
            android:background="@color/transparent"
            android:padding="5dp"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_white_cross" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="40dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_music"
                android:tint="@color/white" />

            <TextView
                android:id="@+id/txt_add_sound"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:text="Add Sound"
                android:textColor="@color/white"
                android:textSize="13dp"
                android:textStyle="bold" />
        </LinearLayout>

        <TextView
            android:id="@+id/next_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/recylerview"
            android:layout_alignParentRight="true"
            android:layout_marginTop="40dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="15dp"
            android:background="@drawable/round_bor_bg"
            android:paddingLeft="20dp"
            android:paddingTop="10dp"
            android:paddingRight="20dp"
            android:paddingBottom="10dp"
            android:text="Next"
            android:textColor="@color/white"
            android:textSize="15dp"
            android:textStyle="bold" />

    </RelativeLayout>
</RelativeLayout>