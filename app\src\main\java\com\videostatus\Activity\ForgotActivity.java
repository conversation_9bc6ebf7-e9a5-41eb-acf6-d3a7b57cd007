package com.videostatus.Activity;

import android.app.ProgressDialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.videostatus.Model.ForgotModel.ForgotModel;
import com.videostatus.Model.SuccessModel.SuccessModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ForgotActivity extends AppCompatActivity {

    EditText et_email;
    String str_email;
    TextView txt_login;
    ProgressDialog progressDialog;
    private PrefManager prefManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.forgotactivity);

//        Init value
        Init();

//        Login Click event
        txt_login.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                str_email = et_email.getText().toString();
                if (TextUtils.isEmpty(str_email)) {
                    Toast.makeText(ForgotActivity.this, "Enter Email Address", Toast.LENGTH_SHORT).show();
                    return;
                }
                ForgotPassword();
            }
        });

    }

    public void Init() {
        prefManager = new PrefManager(this);
        progressDialog = new ProgressDialog(ForgotActivity.this);
        progressDialog.setMessage("Please wait...");
        progressDialog.setCanceledOnTouchOutside(false);

        et_email = findViewById(R.id.et_email);

        txt_login = findViewById(R.id.txt_login);
    }

    public void ForgotPassword() {
        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.forgotpassword(str_email);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                if (response.code() == 200) {
                    progressDialog.dismiss();
                    Toast.makeText(ForgotActivity.this, "" + response.body().getMessage(), Toast.LENGTH_LONG).show();
                    if (response.body().getStatus() == 200)
                        finish();
                }
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
                progressDialog.dismiss();
            }
        });
    }

}
