package com.videostatus.Fragment;

import android.app.Fragment;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.videostatus.Adapter.UserAdapter;
import com.videostatus.Model.UserModel.Result;
import com.videostatus.Model.UserModel.UserModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.util.ArrayList;
import java.util.List;

import ir.alirezabdn.wp7progress.WP10ProgressBar;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import static android.content.Context.MODE_PRIVATE;

public class UserList extends Fragment {

    public UserList() {
    }

    WP10ProgressBar progressBar;
    RecyclerView recycler_user;
    UserAdapter userAdapter;
    private SwipeRefreshLayout swipeContainer;
    List<Result> UserList;
    String id;

    PrefManager prefManager;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View root = inflater.inflate(R.layout.userlist, container, false);

        getActivity().setTitle("Users");

        prefManager = new PrefManager(getActivity());

        progressBar = root.findViewById(R.id.wp7progressBar);
        recycler_user = root.findViewById(R.id.recycler_userlist);

        swipeContainer = (SwipeRefreshLayout) root.findViewById(R.id.swipeContainer);
        swipeContainer.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                swipeContainer.setRefreshing(true);
                get_user_list();
            }
        });
        swipeContainer.setColorSchemeResources(android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light);

        SharedPreferences prefs = getActivity().getSharedPreferences("VideoStatus", MODE_PRIVATE);
        id = prefs.getString("id", "");

        get_user_list();

        return root;
    }


    private void get_user_list() {

        progressBar.showProgressBar();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<UserModel> call = bookNPlayAPI.top_users();
        call.enqueue(new Callback<UserModel>() {
            @Override
            public void onResponse(Call<UserModel> call, Response<UserModel> response) {
                progressBar.hideProgressBar();
                swipeContainer.setRefreshing(false);
                if (response.code() == 200) {
                    UserList = new ArrayList<>();
                    UserList = response.body().getResult();
                    Log.e("UserList", "" + UserList.size());

                    userAdapter = new UserAdapter(getActivity(), UserList);
                    GridLayoutManager manager = new GridLayoutManager(getActivity(), 2, GridLayoutManager.VERTICAL, false);
                    recycler_user.setLayoutManager(manager);
                    recycler_user.setItemAnimator(new DefaultItemAnimator());
                    recycler_user.setAdapter(userAdapter);
                }
            }

            @Override
            public void onFailure(Call<UserModel> call, Throwable t) {
                progressBar.hideProgressBar();
            }
        });
    }
}
