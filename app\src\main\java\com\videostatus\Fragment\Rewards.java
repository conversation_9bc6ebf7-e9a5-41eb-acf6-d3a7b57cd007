package com.videostatus.Fragment;

import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.fragment.app.Fragment;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.videostatus.Activity.MainActivity;
import com.videostatus.Activity.PaymentDetailsActivity;
import com.videostatus.Adapter.CategoryAdapter2;
import com.videostatus.Adapter.RewardAdapter;
import com.videostatus.Adapter.WithdrawalAdapter;
import com.videostatus.Model.RewardModel.Result;
import com.videostatus.Model.RewardModel.RewardModel;
import com.videostatus.Model.WithDrawalModel.WithDrawalModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.util.ArrayList;
import java.util.List;

import ir.alirezabdn.wp7progress.WP10ProgressBar;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class Rewards extends Fragment {

    public Rewards() {
    }

    WP10ProgressBar progressBar;
    RecyclerView rv_points;
    RewardAdapter rewardAdapter;
    List<Result> RewardList;

    List<com.videostatus.Model.WithDrawalModel.Result> WithdrawalList;
    WithdrawalAdapter withdrawalAdapter;

    PrefManager prefManager;

    TextView txt_avg, txt_current_point, txt_withdrawal_point,txt_points;
    TextView txt_current_point_line, txt_withdrawal_point_line;
    LinearLayout ly_reward_claims, ly_current_point, ly_withdrawal_point, ly_no_record;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View root = inflater.inflate(R.layout.rewards, container, false);

        MainActivity.al_title.setVisibility(View.VISIBLE);
        MainActivity.ly_bottom.setBackground(getResources().getDrawable(R.drawable.bottom_gra_bg));
//        MainActivity.bottomNavigationView.setItemIconTintList(getResources().getColor(R.color.white));
        getActivity().setTitle("Rewards");

        init(root);

        return root;
    }

    public void init(View root) {

        prefManager = new PrefManager(getActivity());

        progressBar = root.findViewById(R.id.wp7progressBar);
        rv_points = root.findViewById(R.id.rv_points);

        txt_avg = root.findViewById(R.id.txt_avg);
        txt_current_point = root.findViewById(R.id.txt_current_point);
        txt_withdrawal_point = root.findViewById(R.id.txt_withdrawal_point);

        txt_current_point_line = root.findViewById(R.id.txt_current_point_line);
        txt_withdrawal_point_line = root.findViewById(R.id.txt_withdrawal_point_line);
        ly_reward_claims = root.findViewById(R.id.ly_reward_claims);

        ly_current_point = root.findViewById(R.id.ly_current_point);
        ly_withdrawal_point = root.findViewById(R.id.ly_withdrawal_point);
        ly_no_record = root.findViewById(R.id.ly_no_record);
        txt_points = root.findViewById(R.id.txt_points);

        txt_points.setText("" + prefManager.getValue("earning_point") + " Points = " +
                prefManager.getValue("earning_amount") + " " +
                prefManager.getValue("currency"));

        ly_current_point.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                txt_current_point.setTextColor(getResources().getColor(R.color.colorPrimary));
                txt_current_point_line.setBackground(getResources().getDrawable(R.drawable.reward_gra_bg));

                txt_withdrawal_point.setTextColor(getResources().getColor(R.color.font_dark));
                txt_withdrawal_point_line.setBackground(null);

                if (RewardList.size() > 0) {
                    rewardAdapter = new RewardAdapter(getActivity(), RewardList);
                    rv_points.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.VERTICAL,
                            false));
                    rv_points.setItemAnimator(new DefaultItemAnimator());
                    rv_points.setAdapter(rewardAdapter);
                    rewardAdapter.notifyDataSetChanged();

                    ly_no_record.setVisibility(View.GONE);
                    rv_points.setVisibility(View.VISIBLE);
                } else {
                    ly_no_record.setVisibility(View.VISIBLE);
                    rv_points.setVisibility(View.GONE);
                }

            }
        });

        ly_withdrawal_point.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                txt_withdrawal_point.setTextColor(getResources().getColor(R.color.colorPrimary));
                txt_withdrawal_point_line.setBackground(getResources().getDrawable(R.drawable.reward_gra_bg));

                txt_current_point.setTextColor(getResources().getColor(R.color.font_dark));
                txt_current_point_line.setBackground(null);

                if (WithdrawalList.size() > 0) {
                    withdrawalAdapter = new WithdrawalAdapter(getActivity(), WithdrawalList);
                    rv_points.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.VERTICAL,
                            false));
                    rv_points.setItemAnimator(new DefaultItemAnimator());
                    rv_points.setAdapter(withdrawalAdapter);
                    withdrawalAdapter.notifyDataSetChanged();

                    ly_no_record.setVisibility(View.GONE);
                    rv_points.setVisibility(View.VISIBLE);
                } else {
                    ly_no_record.setVisibility(View.VISIBLE);
                    rv_points.setVisibility(View.GONE);
                }
            }
        });

        ly_reward_claims.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(getActivity(), PaymentDetailsActivity.class));
            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();
        GetReward();

        GetWithdrawal();
    }

    private void GetReward() {

        progressBar.showProgressBar();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<RewardModel> call = bookNPlayAPI.get_rewards("" + prefManager.getLoginId());
        call.enqueue(new Callback<RewardModel>() {
            @Override
            public void onResponse(Call<RewardModel> call, Response<RewardModel> response) {
                progressBar.hideProgressBar();
                if (response.code() == 200) {
                    if (response.body().getStatus() == 200) {
                        RewardList = new ArrayList<>();
                        RewardList = response.body().getResult();
                        Log.e("RewardList", "" + RewardList.size());

                        if(RewardList.size()>0) {
                            txt_avg.setText("" + response.body().getTotal_rewards());

                            rewardAdapter = new RewardAdapter(getActivity(), RewardList);
                            rv_points.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.VERTICAL,
                                    false));
                            rv_points.setItemAnimator(new DefaultItemAnimator());
                            rv_points.setAdapter(rewardAdapter);
                            rewardAdapter.notifyDataSetChanged();

                            ly_no_record.setVisibility(View.GONE);
                            rv_points.setVisibility(View.VISIBLE);
                        }else{
                            ly_no_record.setVisibility(View.VISIBLE);
                            rv_points.setVisibility(View.GONE);
                        }
                    } else {
                        ly_no_record.setVisibility(View.VISIBLE);
                        rv_points.setVisibility(View.GONE);
                    }
                }
            }

            @Override
            public void onFailure(Call<RewardModel> call, Throwable t) {
                progressBar.hideProgressBar();
            }
        });
    }


    private void GetWithdrawal() {

        progressBar.showProgressBar();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<WithDrawalModel> call = bookNPlayAPI.withdrawal_list("" + prefManager.getLoginId());
        call.enqueue(new Callback<WithDrawalModel>() {
            @Override
            public void onResponse(Call<WithDrawalModel> call, Response<WithDrawalModel> response) {
                progressBar.hideProgressBar();
                if (response.code() == 200) {
                    WithdrawalList = new ArrayList<>();
                    WithdrawalList = response.body().getResult();
                    Log.e("WithdrawalList", "" + WithdrawalList.size());

//                    withdrawalAdapter = new WithdrawalAdapter(getActivity(), WithdrawalList);
//                    rv_points.setLayoutManager(new LinearLayoutManager(getActivity(), LinearLayoutManager.VERTICAL,
//                            true));
//                    rv_points.setItemAnimator(new DefaultItemAnimator());
//                    rv_points.setAdapter(withdrawalAdapter);
//                    withdrawalAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(Call<WithDrawalModel> call, Throwable t) {
                progressBar.hideProgressBar();
            }
        });
    }

}
