<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


<LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_login_patter"
        android:orientation="vertical"
        android:weightSum="1.0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="0.4"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="70dp"
                android:src="@drawable/login_logo" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:layout_weight="0.3"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="25dp"
                android:layout_marginLeft="2dp"
                android:fontFamily="@font/public_medium"
                android:gravity="center_vertical"
                android:text="Email Address"
                android:textColor="@color/font_dark" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@drawable/edittext_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:weightSum="1.0">

                <EditText
                    android:id="@+id/et_email"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="5dp"
                    android:background="@null"
                    android:fontFamily="@font/public_medium"
                    android:hint="Email"
                    android:paddingLeft="5dp"
                    android:singleLine="true"
                    android:textColor="@color/font_dark"
                    android:textColorHint="@color/font_light"
                    android:textSize="14dp" />
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="25dp"
                android:layout_marginLeft="2dp"
                android:layout_marginTop="5dp"
                android:fontFamily="@font/public_medium"
                android:gravity="center_vertical"
                android:text="Password"
                android:textColor="@color/font_dark" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@drawable/edittext_bg"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:weightSum="1.0">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <EditText
                        android:id="@+id/et_password"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="5dp"
                        android:background="@null"
                        android:fontFamily="@font/public_medium"
                        android:hint="Password"
                        android:inputType="textPassword"
                        android:paddingLeft="5dp"
                        android:singleLine="true"
                        android:textColor="@color/font_dark"
                        android:textColorHint="@color/font_light"
                        android:textSize="14dp" />

                    <TextView
                        android:id="@+id/txt_forgot"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentEnd="true"
                        android:layout_centerInParent="true"
                        android:layout_marginEnd="10dp"
                        android:fontFamily="@font/public_medium"
                        android:gravity="right|center"
                        android:text="Forgot Password?"
                        android:textColor="@color/colorPrimary"
                        android:textSize="12dp" />
                </RelativeLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/white" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="15dp"
            android:layout_weight="0.3"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="25dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1">

                    <CheckBox
                        android:id="@+id/checkBox"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:textColor="@color/white"
                        android:theme="@style/YellowCheck" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:text="Remember Me"
                        android:textColor="@color/font_dark"
                        android:textSize="12dp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical">


                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="1.0">

                <TextView
                    android:id="@+id/txt_login"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/round_bor_bg"
                    android:fontFamily="@font/public_bold"
                    android:gravity="center"
                    android:text="Login"
                    android:textColor="@color/white"
                    android:textSize="14dp" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1">

                        <FrameLayout
                            android:id="@+id/FrameLayout1"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="5dp"
                            android:background="@drawable/round_gmail">

                            <com.google.android.gms.common.SignInButton
                                android:id="@+id/sign_in_button"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:visibility="gone" />

                            <ImageView
                                android:id="@+id/btn_google"
                                android:layout_width="match_parent"
                                android:layout_height="45dp"
                                android:gravity="center"
                                android:onClick="onClick"
                                android:padding="10dp"
                                android:src="@drawable/ic_google"
                                android:textAllCaps="false"
                                android:textSize="14dp"
                                android:visibility="visible" />

                        </FrameLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="45dp"
                        android:layout_gravity="center_vertical"
                        android:layout_weight="1">

                        <FrameLayout
                            android:id="@+id/FrameLayout2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="5dp"
                            android:layout_marginRight="5dp">

                            <com.facebook.login.widget.LoginButton
                                android:id="@+id/login_button"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:visibility="gone" />

                            <ImageView
                                android:id="@+id/fb"
                                android:layout_width="match_parent"
                                android:layout_height="45dp"
                                android:background="@drawable/round_fb"
                                android:onClick="onClickFacebookButton"
                                android:padding="10dp"
                                android:src="@drawable/ic_fb_status"
                                android:textAllCaps="false"
                                android:textColor="#ffffff"
                                android:visibility="visible" />
                        </FrameLayout>
                    </LinearLayout>

                </LinearLayout>


                <TextView
                    android:id="@+id/txt_skip"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:background="@drawable/round_border"
                    android:fontFamily="@font/public_bold"
                    android:gravity="center"
                    android:text="Skip"
                    android:textColor="@color/font_dark"
                    android:textSize="14dp" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:letterSpacing="0.05"
                        android:text="Don't have an account ?"
                        android:textColor="@color/font_dark"
                        android:textSize="14dp" />

                    <TextView
                        android:id="@+id/txt_already_signup"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:letterSpacing="0.05"
                        android:text=" Signup"
                        android:textColor="@color/colorPrimary"
                        android:textSize="14dp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>


    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>