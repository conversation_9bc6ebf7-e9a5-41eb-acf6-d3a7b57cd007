package com.videostatus.Adapter;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.iarcuschin.simpleratingbar.SimpleRatingBar;
import com.squareup.picasso.Picasso;
import com.videostatus.Activity.HomeSingalVideo;
import com.videostatus.Model.VideoModel.Result;
import com.videostatus.R;
import com.videostatus.Utility.Constant;
import com.videostatus.Interface.ShareAll;

import java.util.List;

public class NewArrivalAdapter extends RecyclerView.Adapter<NewArrivalAdapter.MyViewHolder> {

    private List<Result> NewArrivalList;
    Context mcontext;

    ShareAll shareAll;

    public class MyViewHolder extends RecyclerView.ViewHolder {
        TextView txt_title, txt_bookmark, txt_view, txt_avg;
        ImageView iv_thumb;
        CardView cardview;
        SimpleRatingBar ratingbar;

        public MyViewHolder(View view) {
            super(view);
            txt_bookmark = (TextView) view.findViewById(R.id.txt_bookmark);
            iv_thumb = (ImageView) view.findViewById(R.id.iv_thumb);
            txt_title = (TextView) view.findViewById(R.id.txt_title);
            txt_view = (TextView) view.findViewById(R.id.txt_view);
            txt_avg = (TextView) view.findViewById(R.id.txt_avg);
            cardview = (CardView) view.findViewById(R.id.cardview);
            ratingbar = view.findViewById(R.id.ratingbar);
        }
    }


    public NewArrivalAdapter(Context context, List<Result> NewArrivalList, ShareAll shareAll) {
        this.NewArrivalList = NewArrivalList;
        this.mcontext = context;
        this.shareAll = shareAll;
    }

    @Override
    public MyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.newarrical_item_row, parent, false);

        return new MyViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(final MyViewHolder holder, final int position) {

        holder.txt_title.setText(NewArrivalList.get(position).getVideoTitle());
        holder.txt_view.setText(withSuffix(Long.parseLong("" + NewArrivalList.get(position).getView())) + "");
        holder.ratingbar.setRating(Float.parseFloat(NewArrivalList.get(position).getAvgRating()));
        holder.txt_avg.setText(" " + NewArrivalList.get(position).getAvgRating());

        Picasso.with(mcontext).load(NewArrivalList.get(position).getThumbnailImg())
                .resize(400, 400)
                .placeholder(R.drawable.no_image).centerCrop().into(holder.iv_thumb);

//        holder.ratingbar.setRating(Float.parseFloat(NewArrivalList.get(position).geta));

        holder.txt_bookmark.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
            }
        });

        holder.cardview.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Constant.VideoList = NewArrivalList;
                Intent intent = new Intent(mcontext, HomeSingalVideo.class);
                intent.putExtra("position", position);
                mcontext.startActivity(intent);
            }
        });
    }

    @Override
    public int getItemCount() {
        return NewArrivalList.size();
    }

    public String withSuffix(long count) {
        if (count < 1000) return "" + count;
        int exp = (int) (Math.log(count) / Math.log(1000));
        return String.format("%.1f %c",
                count / Math.pow(1000, exp),
                "kMGTPE".charAt(exp - 1));
    }

}
