package com.videostatus.Activity;

import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.PorterDuff;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Environment;
import android.text.Html;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.iarcuschin.simpleratingbar.SimpleRatingBar;
import com.makeramen.roundedimageview.RoundedImageView;
import com.potyvideo.library.AndExoPlayerView;
import com.squareup.picasso.Picasso;
import com.videostatus.Adapter.CategoryVideoAdapter;
import com.videostatus.Adapter.CommentAdapter;
import com.videostatus.Model.CommentModel.CommentModel;
import com.videostatus.Model.SuccessModel.SuccessModel;
import com.videostatus.Model.VideoDetailsModel.VideoDetailsModel;
import com.videostatus.Model.VideoModel.Result;
import com.videostatus.Model.VideoModel.VideoModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.Interface.ShareAll;
import com.videostatus.Utility.Utils;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Formatter;
import java.util.List;
import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class StatusDetails extends AppCompatActivity implements ShareAll {

    TextView txt_download, txt_view, txt_title, txt_avg, txt_like_cnt,
            txt_dislike_cnt, txt_download_cnt, txt_back, txt_fav;

    ImageView iv_whatsapp, iv_fb, iv_insta, iv_twitter;

    SimpleRatingBar ratingbar;
    LinearLayout ly_share;

    RecyclerView recycler_related;
    ImageView videoView;
    ImageView iv_play;

    ProgressDialog mProgressDialog;
    PrefManager prefManager;

    String v_id, cat_id, user_id, video_url;

    private static final String WHATSAPP_KEY = "com.whatsapp";

    String Type = "";
    ProgressDialog progressDialog;
    List<Result> RelatedVideoList;

    List<com.videostatus.Model.CommentModel.Result> CommentList;
    CommentAdapter commentAdapter;
    RecyclerView rv_comment;

    AndExoPlayerView andExoPlayerView;

    private StringBuilder mFormatBuilder;
    private Formatter mFormatter;

    EditText et_comment;
    LinearLayout ly_send, ly_like, ly_dislike, ly_download;

    RoundedImageView iv_user_thumb;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.status_details);

        Toolbar toolbar = (Toolbar) findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("Video Details");

        toolbar.setTitleTextColor(getResources().getColor(R.color.white));
        toolbar.getNavigationIcon().setColorFilter(getResources().getColor(R.color.white), PorterDuff.Mode.SRC_ATOP);

        iv_user_thumb = findViewById(R.id.iv_user_thumb);
        prefManager = new PrefManager(StatusDetails.this);

        Init();

        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            v_id = bundle.getString("id");
            Log.e("id", "" + v_id);
//
//            txt_title.setText(video_title);
//            txt_view.setText(view);
//            txt_download.setText("" + dowanload);

            VideoDetails();

            Comments();

        }

        ly_send.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (et_comment.getText().toString().length() > 0) {
                    AddComments("" + et_comment.getText().toString());
                }
            }
        });

        txt_back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        iv_user_thumb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(StatusDetails.this, Profile.class);
                intent.putExtra("Id", "" + user_id);
                startActivity(intent);
            }
        });
    }

    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                finish();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    public void Init() {

        progressDialog = new ProgressDialog(StatusDetails.this);
        progressDialog.setMessage("Please wait...");
        progressDialog.setCanceledOnTouchOutside(false);

        andExoPlayerView = findViewById(R.id.andExoPlayerView);
        iv_play = findViewById(R.id.iv_play);
        videoView = findViewById(R.id.iv_thumb);
        ly_share = findViewById(R.id.ly_share);

        recycler_related = findViewById(R.id.recycler_related);
        txt_download = findViewById(R.id.txt_download);
        txt_view = findViewById(R.id.txt_view);
        txt_title = findViewById(R.id.txt_title);

        ratingbar = findViewById(R.id.ratingbar);
        txt_avg = findViewById(R.id.txt_avg);
        txt_like_cnt = findViewById(R.id.txt_like_cnt);
        txt_dislike_cnt = findViewById(R.id.txt_dislike_cnt);
        txt_download_cnt = findViewById(R.id.txt_download_cnt);

        txt_fav = findViewById(R.id.txt_fav);
        txt_back = findViewById(R.id.txt_back);
        rv_comment = findViewById(R.id.rv_comment);
        ly_send = findViewById(R.id.ly_send);
        et_comment = findViewById(R.id.et_comment);

        ly_like = findViewById(R.id.ly_like);
        ly_dislike = findViewById(R.id.ly_dislike);
        ly_download = findViewById(R.id.ly_download);

        iv_whatsapp = findViewById(R.id.iv_whatsapp);
        iv_fb = findViewById(R.id.iv_fb);
        iv_insta = findViewById(R.id.iv_insta);
        iv_twitter=findViewById(R.id.iv_twitter);

        iv_whatsapp.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                    Log.e("file", "" + (CheckFile(video_url)));
                    if (CheckFile(video_url)) {
                        File file = new File(GetFilePath(video_url));
                        ShareOnWhatsApp(file);
                    } else {
                        Type = "whatsapp";
                        new DownloadFile().execute(video_url);
                    }
                } else {
                    startActivity(new Intent(StatusDetails.this, LoginActivity.class));
                }
            }
        });

        iv_fb.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                    Log.e("file", "" + (CheckFile(video_url)));
                    if (CheckFile(video_url)) {
                        File file = new File(GetFilePath(video_url));
                        ShareOnFacebook(file);
                    } else {
                        Type = "facebook";
                        new DownloadFile().execute(video_url);
                    }
                } else {
                    startActivity(new Intent(StatusDetails.this, LoginActivity.class));
                }
            }
        });

        iv_insta.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                    Log.e("file", "" + (CheckFile(video_url)));
                    if (CheckFile(video_url)) {
                        File file = new File(GetFilePath(video_url));
                        ShareOnInsta(file);
                    } else {
                        Type = "instagram";
                        new DownloadFile().execute(video_url);
                    }
                } else {
                    startActivity(new Intent(StatusDetails.this, LoginActivity.class));
                }
            }
        });

        iv_twitter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                    Log.e("file", "" + (CheckFile(video_url)));
                    if (CheckFile(video_url)) {
                        File file = new File(GetFilePath(video_url));
                        ShareOnTwitter(file);
                    } else {
                        Type = "twitter";
                        new DownloadFile().execute(video_url);
                    }
                } else {
                    startActivity(new Intent(StatusDetails.this, LoginActivity.class));
                }
            }
        });

        iv_play.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                iv_play.setVisibility(View.GONE);
                videoView.setVisibility(View.GONE);

                andExoPlayerView.setVisibility(View.VISIBLE);
                andExoPlayerView.setSource(video_url);
                andExoPlayerView.setPlayWhenReady(true);

                ViewAPI();
            }
        });

        ly_like.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                    Log.e("file", "" + (CheckFile(video_url)));
                    LikeDislike("1");
                } else {
                    startActivity(new Intent(StatusDetails.this, LoginActivity.class));
                }
            }
        });

        ly_dislike.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                    Log.e("file", "" + (CheckFile(video_url)));
                    LikeDislike("2");
                } else {
                    startActivity(new Intent(StatusDetails.this, LoginActivity.class));
                }
            }
        });

        ly_download.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                    Log.e("file", "" + (CheckFile(video_url)));
                    if (CheckFile(video_url)) {
                        Toast.makeText(StatusDetails.this, "Already Downloaded", Toast.LENGTH_SHORT)
                                .show();
                    } else {
                        Type = "download";
                        new DownloadFile().execute(video_url);
                    }
                } else {
                    startActivity(new Intent(StatusDetails.this, LoginActivity.class));
                }

            }
        });

        txt_fav.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!prefManager.getLoginId().equalsIgnoreCase("0")) {
                    Add_favorite();
                } else {
                    startActivity(new Intent(StatusDetails.this, LoginActivity.class));
                }
            }
        });

        ly_share.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    Intent sharingIntent = new Intent(Intent.ACTION_SEND);
                    sharingIntent.setType("text/html");
                    sharingIntent.putExtra(android.content.Intent.EXTRA_TEXT, Html.fromHtml("" + video_url));
                    startActivity(Intent.createChooser(sharingIntent, "Share using"));
                } catch (Exception e) {
                    Log.e("Exception", "" + e.getMessage());
                }
            }
        });

//        ratingbar.setOnRatingBarChangeListener(new SimpleRatingBar.OnRatingBarChangeListener() {
//            @Override
//            public void onRatingChanged(SimpleRatingBar simpleRatingBar, float rating, boolean fromUser) {
//                Log.e("rating", "" + rating);
//                add_ratings(""+rating);
//            }
//        });
    }

    public void VideoDetails() {
        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<VideoDetailsModel> call = bookNPlayAPI.get_video_detail(v_id);
        call.enqueue(new Callback<VideoDetailsModel>() {
            @Override
            public void onResponse(Call<VideoDetailsModel> call, Response<VideoDetailsModel> response) {
                progressDialog.hide();
                if (response.code() == 200) {

                    if (response.body().getStatus() == 200) {

                        Log.e("Image", "" + response.body().getResult().getThumbnailImg());
                        Picasso.with(StatusDetails.this).load(response.body().getResult().getThumbnailImg())
                                .into(videoView);

                        Picasso.with(StatusDetails.this).load(response.body().getResult().getProfile_img())
                                .into(iv_user_thumb);

                        cat_id = response.body().getResult().getCatId();
                        user_id = response.body().getResult().getUserId();
                        video_url = response.body().getResult().getVideoUrl();

                        Log.e("video_url", "" + video_url);

                        txt_title.setText("" + response.body().getResult().getVideoTitle());
                        txt_view.setText(" " + Utils.withSuffix(Long.parseLong("" + response.body().getResult().getView())) + " Views | "
                                + Utils.withSuffix(Long.parseLong("" + response.body().getResult().getTotalComment())) + " Comments");

                        ratingbar.setRating(Float.parseFloat("" + response.body().getResult().getAvgRating()));
                        txt_avg.setText("" + response.body().getResult().getAvgRating());
                        txt_like_cnt.setText("" + response.body().getResult().getTotalLike() + " Likes");
                        txt_dislike_cnt.setText("" + response.body().getResult().getTotalDislike() + " Dislikes");
                        txt_download_cnt.setText("" + response.body().getResult().getDowanload() + " Downloads");

                        if (response.body().getResult().getIsFavorite().equalsIgnoreCase("1")) {
                            txt_fav.setBackground(getResources().getDrawable(R.drawable.ic_bookmark_fill));
                        } else {
                            txt_fav.setBackground(getResources().getDrawable(R.drawable.ic_bookmark));
                        }

//                        andExoPlayerView.setSource(video_url);
//                        andExoPlayerView.setPlayWhenReady(false);

                        getRelated();
                    }
                }
            }

            @Override
            public void onFailure(Call<VideoDetailsModel> call, Throwable t) {
                progressDialog.hide();
            }
        });
    }

    @Override
    public void WhatsappShare() {

    }

    private class DownloadFile extends AsyncTask<String, String, String> {

        private ProgressDialog progressDialog;
        private String fileName;
        private String folder;
        private boolean isDownloaded;

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            this.progressDialog = new ProgressDialog(StatusDetails.this);
            this.progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
            this.progressDialog.setCancelable(false);
            this.progressDialog.show();
        }

        @Override
        protected String doInBackground(String... f_url) {
            int count;
            try {
                URL url = new URL(f_url[0]);
                URLConnection connection = url.openConnection();
                connection.connect();
                // getting file length
                int lengthOfFile = connection.getContentLength();
                // input stream to read file - with 8k buffer
                InputStream input = new BufferedInputStream(url.openStream(), 8192);
                String timestamp = new SimpleDateFormat("yy_MM_dd_HH_mm_ss").format(new Date());
                //Extract file name from URL
                fileName = f_url[0].substring(f_url[0].lastIndexOf('/') + 1, f_url[0].length());
                //Append timestamp to file name
//                fileName = timestamp + "_" + fileName;
                //External directory path to save file
                Log.e("fileName", fileName);
                folder = Environment.getExternalStorageDirectory() + File.separator + getResources().getString(R.string.app_name) + "/";
                //Create androiddeft folder if it does not exist
                File directory = new File(folder);

                if (!directory.exists()) {
                    directory.mkdirs();
                }
                // Output stream to write file
                OutputStream output = new FileOutputStream(folder + fileName);
                byte data[] = new byte[1024];
                long total = 0;
                while ((count = input.read(data)) != -1) {
                    total += count;
                    // publishing the progress....
                    // After this onProgressUpdate will be called
                    publishProgress("" + (int) ((total * 100) / lengthOfFile));
                    Log.e("==>Pro==>", "Progress: " + (int) ((total * 100) / lengthOfFile));
                    // writing data to file
                    output.write(data, 0, count);
                }
                // flushing output
                output.flush();
                // closing streams
                output.close();
                input.close();
                return "" + folder + fileName;

            } catch (Exception e) {
                Log.e("Error: ", e.getMessage());
            }
            return "Something went wrong";
        }

        protected void onProgressUpdate(String... progress) {
            // setting progress percentage
            progressDialog.setProgress(Integer.parseInt(progress[0]));
        }

        @Override
        protected void onPostExecute(String message) {
            // dismiss the dialog after the file was downloaded
            this.progressDialog.dismiss();
            // Display File path after downloading
            Log.e("file-path", "" + message);
//            Toast.makeText(getApplicationContext(),
//                    message, Toast.LENGTH_LONG).show();
            File file = new File(message);
            Log.e("file", "" + file);

            if (Type.equalsIgnoreCase("whatsapp"))
                ShareOnWhatsApp(file);
            else if (Type.equalsIgnoreCase("facebook"))
                ShareOnFacebook(file);
            else if (Type.equalsIgnoreCase("facebook_messanger"))
                ShareOnFacebookMessanger(file);
            else if (Type.equalsIgnoreCase("instagram"))
                ShareOnInsta(file);
            else if (Type.equalsIgnoreCase("twitter"))
                ShareOnTwitter(file);
            else if (Type.equalsIgnoreCase("snapchat"))
                ShareOnSnap(file);
            else if (Type.equalsIgnoreCase("share"))
                ShareOnShare(file);
            else if (Type.equalsIgnoreCase("download")) {
                DownloadAPI();
                Toast.makeText(StatusDetails.this, "Download at:-" + message, Toast.LENGTH_SHORT).show();
            } else if (Type.equalsIgnoreCase("facebook_messanger"))
                ShareOnFacebookMessanger(file);
        }
    }

    public void ShareOnWhatsApp(File file) {
        try {
            Uri videoUri = FileProvider.getUriForFile(
                    StatusDetails.this, getApplicationContext().getPackageName() + ".provider", file);
            Log.e("uri", "" + videoUri);
            Intent videoshare = new Intent(Intent.ACTION_SEND);
            videoshare.setType("*/*");
            videoshare.setPackage("com.whatsapp");
            videoshare.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            videoshare.putExtra(Intent.EXTRA_STREAM, videoUri);
            startActivity(videoshare);
        } catch (Exception e) {
            Log.e("Exception", "" + e.getMessage());
            Toast.makeText(StatusDetails.this, "WhatsApp not available", Toast.LENGTH_SHORT).show();
        }
    }

    public void ShareOnFacebook(File file) {
        try {
            Uri videoUri = FileProvider.getUriForFile(
                    StatusDetails.this, getApplicationContext().getPackageName() + ".provider", file);
            Log.e("uri", "" + videoUri);

            Uri imageUri = FileProvider.getUriForFile(StatusDetails.this,
                    StatusDetails.this.getApplicationContext().getPackageName() + ".provider", file);
            Intent shareIntent = new Intent();
            shareIntent.setAction(Intent.ACTION_SEND);
            shareIntent.setPackage("com.facebook.katana");
            shareIntent.putExtra(Intent.EXTRA_STREAM, imageUri);
            shareIntent.setType("video/mp4");
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            startActivity(shareIntent);
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("ActivityNotF", "" + ex.getMessage());
            alertForApp(getString(R.string.install_facebook), "com.facebook.katana");
        }
    }

    public void ShareOnFacebookMessanger(File file) {
        try {
            Uri videoUri = FileProvider.getUriForFile(
                    StatusDetails.this, getApplicationContext().getPackageName() + ".provider", file);
            Log.e("uri", "" + videoUri);
            Intent shareIntent = new Intent();
            shareIntent.setAction(Intent.ACTION_SEND);
            shareIntent.setPackage("com.facebook.orca");
            shareIntent.putExtra(Intent.EXTRA_STREAM, videoUri);
            shareIntent.setType("video/mp4");
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            startActivity(shareIntent);
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("Activity", "" + ex.getMessage());
            alertForApp(getString(R.string.install_messenger), "com.facebook.orca");
        }
    }

    public void ShareOnInsta(File file) {

        try {
            Uri videoUri = FileProvider.getUriForFile(
                    StatusDetails.this, getApplicationContext().getPackageName() + ".provider", file);
            Log.e("uri", "" + videoUri);
            Intent shareIntent = new Intent();
            shareIntent.setAction(Intent.ACTION_SEND);
            shareIntent.setPackage("com.instagram.android");
            shareIntent.putExtra(Intent.EXTRA_STREAM, videoUri);
            shareIntent.setType("video/mp4");
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            startActivity(shareIntent);
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("ActivityNotF", "" + ex.getMessage());
            alertForApp(getString(R.string.install_insta), "com.instagram.android");
        }

    }

    public void ShareOnTwitter(File file) {
        Uri videoUri = FileProvider.getUriForFile(
                StatusDetails.this, getApplicationContext().getPackageName() + ".provider", file);
        Log.e("uri", "" + videoUri);
        Intent shareIntent = new Intent();
        shareIntent.setAction(Intent.ACTION_SEND);
        shareIntent.setPackage("com.twitter.android");
        shareIntent.putExtra(Intent.EXTRA_STREAM, videoUri);
        shareIntent.setType("video/mp4");
        shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        try {
            startActivity(shareIntent);
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("ActivityNotF", "" + ex.getMessage());
            alertForApp(getString(R.string.install_twitter), "com.twitter.android");
        }
    }

    public void ShareOnSnap(File file) {
        Uri videoUri = FileProvider.getUriForFile(
                StatusDetails.this, getApplicationContext().getPackageName() + ".provider", file);
        Log.e("uri", "" + videoUri);
        Intent shareIntent = new Intent();
        shareIntent.setAction(Intent.ACTION_SEND);
        shareIntent.setPackage("com.snapchat.android");
        shareIntent.putExtra(Intent.EXTRA_STREAM, videoUri);
        shareIntent.setType("video/mp4");
        shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        try {
            startActivity(shareIntent);
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("ActivityNotF", "" + ex.getMessage());
            alertForApp(getString(R.string.install_snap), "com.snapchat.android");
        }
    }

    public void ShareOnShare(File file) {
        Uri videoUri = FileProvider.getUriForFile(
                StatusDetails.this, getApplicationContext().getPackageName() + ".provider", file);
        Log.e("uri", "" + videoUri);
        Intent shareIntent = new Intent(
                android.content.Intent.ACTION_SEND);
        shareIntent.setType("video/*");
        shareIntent.putExtra(Intent.EXTRA_STREAM, videoUri);
        shareIntent
                .addFlags(Intent.FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET);
        try {
            startActivity(Intent.createChooser(shareIntent, "Share with"));
        } catch (android.content.ActivityNotFoundException ex) {
            Log.e("ActivityNotF", "" + ex.getMessage());
        }
    }

    private void alertForApp(String errorMessage, final String packageToRedirect) {
        AlertDialog.Builder builder = new AlertDialog.Builder(StatusDetails.this);
        builder.setTitle(R.string.app_not_found);
        builder.setIcon(R.drawable.ic_error);
        builder.setMessage(errorMessage);
        builder.setCancelable(false);
        builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int arg1) {
                String url = getString(R.string.playStore_address) + packageToRedirect;
                Intent i = new Intent(Intent.ACTION_VIEW);
                i.setData(Uri.parse(url));
                startActivity(i);
                dialog.cancel();
            }
        }).setNegativeButton("CANCEl", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int arg1) {
                dialog.cancel();
            }
        });
        AlertDialog alertDialog = builder.create();
        alertDialog.show();

    }

    private void getRelated() {

        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<VideoModel> call = bookNPlayAPI.related_video(cat_id);
        call.enqueue(new Callback<VideoModel>() {
            @Override
            public void onResponse(Call<VideoModel> call, Response<VideoModel> response) {
                progressDialog.hide();
                if (response.code() == 200) {
                    RelatedVideoList = new ArrayList<>();
                    RelatedVideoList = response.body().getResult();
                    Log.e("RelatedVideoList", "" + RelatedVideoList.size());

                    CategoryVideoAdapter categoryVideoAdapter = new CategoryVideoAdapter(StatusDetails.this,
                            RelatedVideoList, "Home");
                    recycler_related.setLayoutManager(new LinearLayoutManager(StatusDetails.this, LinearLayoutManager.HORIZONTAL,
                            false));
                    recycler_related.setItemAnimator(new DefaultItemAnimator());
                    recycler_related.setAdapter(categoryVideoAdapter);
                    categoryVideoAdapter.notifyDataSetChanged();

                }
            }

            @Override
            public void onFailure(Call<VideoModel> call, Throwable t) {
                progressDialog.hide();
            }
        });
    }

    private String stringForTime(int timeMs) {
        mFormatBuilder = new StringBuilder();
        mFormatter = new Formatter(mFormatBuilder, Locale.getDefault());
        int totalSeconds = timeMs / 1000;

        int seconds = totalSeconds % 60;
        int minutes = (totalSeconds / 60) % 60;
        int hours = totalSeconds / 3600;

        mFormatBuilder.setLength(0);
        if (hours > 0) {
            return mFormatter.format("%d:%02d:%02d", hours, minutes, seconds).toString();
        } else {
            return mFormatter.format("%02d:%02d", minutes, seconds).toString();
        }
    }

    public void ViewAPI() {
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.video_view(v_id);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                if (response.code() == 200) {
                }
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
            }
        });
    }

    public void DownloadAPI() {
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.add_dowanload(v_id, user_id);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                if (response.code() == 200) {
                    VideoDetails();
                }
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
            }
        });
    }

    private void Comments() {
        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<CommentModel> call = bookNPlayAPI.view_comment(v_id);
        call.enqueue(new Callback<CommentModel>() {
            @Override
            public void onResponse(Call<CommentModel> call, Response<CommentModel> response) {
                if (response.code() == 200) {

                    Log.e("Related_Item", "" + response);

                    CommentList = new ArrayList<>();
                    CommentList = response.body().getResult();
                    Log.e("CommentList", "" + CommentList.size());

                    commentAdapter = new CommentAdapter(StatusDetails.this, CommentList);
                    rv_comment.setHasFixedSize(true);
                    RecyclerView.LayoutManager mLayoutManager3 = new LinearLayoutManager(StatusDetails.this,
                            LinearLayoutManager.VERTICAL, false);
                    rv_comment.setLayoutManager(mLayoutManager3);
                    rv_comment.setItemAnimator(new DefaultItemAnimator());
                    rv_comment.setAdapter(commentAdapter);
                    commentAdapter.notifyDataSetChanged();

                }
                progressDialog.dismiss();
            }

            @Override
            public void onFailure(Call<CommentModel> call, Throwable t) {
                progressDialog.dismiss();
            }
        });
    }

    private void AddComments(String comment) {
        progressDialog.show();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.add_comment(v_id,
                prefManager.getLoginId(), comment);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                progressDialog.dismiss();
                Log.e("Add Comments", "" + response.body().getMessage());
                Toast.makeText(StatusDetails.this, "" + response.body().getMessage(), Toast.LENGTH_SHORT).show();
                et_comment.setText("");
                VideoDetails();
                Comments();
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
                progressDialog.dismiss();
            }
        });
    }

    private void LikeDislike(String status) {
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.like_dislike(v_id,
                prefManager.getLoginId(), status);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                Log.e("Like DisLike", "" + response.body().getMessage());
                VideoDetails();
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
            }
        });
    }

    private void add_ratings(String rating) {
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.add_ratings(v_id,
                prefManager.getLoginId(), rating);
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                Log.e("Like DisLike", "" + response.body().getMessage());
                VideoDetails();
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
            }
        });
    }

    private void Add_favorite() {
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<SuccessModel> call = bookNPlayAPI.add_favorite(v_id,
                prefManager.getLoginId());
        call.enqueue(new Callback<SuccessModel>() {
            @Override
            public void onResponse(Call<SuccessModel> call, Response<SuccessModel> response) {
                Log.e("Like DisLike", "" + response.body().getMessage());
                VideoDetails();
            }

            @Override
            public void onFailure(Call<SuccessModel> call, Throwable t) {
            }
        });
    }

    public boolean CheckFile(String url1) {
        try {

            String fileName = url1.substring(url1.lastIndexOf('/') + 1,
                    url1.length());
            Log.e("fileName", "" + fileName);
            String folder = Environment.getExternalStorageDirectory() + File.separator + getResources().getString(R.string.app_name) + "/";
            //Create androiddeft folder if it does not exist
            File directory = new File(folder, fileName);

            Log.e("directory", "" + directory);

            if (directory.exists()) {
                return true;
            } else {
                return false;
            }

        } catch (Exception e) {
            Log.e("Exception", "" + e.getMessage());
        }
        return false;
    }

    public String GetFilePath(String url1) {
        try {

            String fileName = url1.substring(url1.lastIndexOf('/') + 1,
                    url1.length());
            Log.e("fileName", "" + fileName);
            String folder = Environment.getExternalStorageDirectory() + File.separator + getResources().getString(R.string.app_name) + "/";
            //Create androiddeft folder if it does not exist
            File directory = new File(folder, fileName);

            Log.e("directory", "" + directory);

            return "" + directory;

        } catch (Exception e) {
            Log.e("Exception", "" + e.getMessage());
        }
        return "";
    }

}
