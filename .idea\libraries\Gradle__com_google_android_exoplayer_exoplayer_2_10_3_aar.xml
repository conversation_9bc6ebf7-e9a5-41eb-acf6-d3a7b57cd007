<component name="libraryTable">
  <library name="Gradle: com.google.android.exoplayer:exoplayer:2.10.3@aar">
    <CLASSES>
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/c4158be49667274df579d7e7831aa8d8/jetified-exoplayer-2.10.3/AndroidManifest.xml" />
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/c4158be49667274df579d7e7831aa8d8/jetified-exoplayer-2.10.3/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-2/files-2.1/c4158be49667274df579d7e7831aa8d8/jetified-exoplayer-2.10.3/res" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer/2.10.3/d29764f0d18f30c3b51fa8f6d328fc55261373f1/exoplayer-2.10.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/com.google.android.exoplayer/exoplayer/2.10.3/d29764f0d18f30c3b51fa8f6d328fc55261373f1/exoplayer-2.10.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>