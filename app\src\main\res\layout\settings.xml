<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <ScrollView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="15dp"
                    android:elevation="5dp"
                    app:cardCornerRadius="10dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/white"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/txt_profile"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/public_regular"
                            android:paddingTop="15dp"
                            android:paddingBottom="15dp"
                            android:text="@string/profile"
                            android:textColor="@color/font_dark"
                            android:textSize="16sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.1dp"
                            android:background="@color/view_setting" />

                        <TextView
                            android:id="@+id/txt_my_favorite"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/public_regular"
                            android:paddingTop="15dp"
                            android:paddingBottom="15dp"
                            android:text="@string/my_favorite"
                            android:visibility="gone"
                            android:textColor="@color/font_dark"
                            android:textSize="16sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:visibility="gone"
                            android:background="@color/view_setting" />

                        <TextView
                            android:id="@+id/my_downloaded_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/public_regular"
                            android:paddingTop="15dp"
                            android:paddingBottom="15dp"
                            android:visibility="gone"
                            android:text="@string/my_downloaded_status"
                            android:textColor="@color/font_dark"
                            android:textSize="16sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.1dp"
                            android:visibility="gone"
                            android:background="@color/view_setting" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="15dp"
                            android:paddingBottom="15dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:layout_toStartOf="@+id/switch_push"
                                android:fontFamily="@font/public_regular"
                                android:text="@string/enable_push_notification"
                                android:textColor="@color/font_dark"
                                android:textSize="16sp" />

                            <androidx.appcompat.widget.SwitchCompat
                                android:id="@+id/switch_push"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:theme="@style/SwitchCompatTheme" />

                        </RelativeLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.1dp"
                            android:background="@color/view_setting" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="15dp"
                            android:paddingBottom="15dp"
                            android:visibility="gone">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerVertical="true"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:layout_toStartOf="@+id/switch_theme"
                                android:text="@string/enable_theme"
                                android:textColor="@color/font_dark"
                                android:textSize="16sp" />

                            <androidx.appcompat.widget.SwitchCompat
                                android:id="@+id/switch_theme"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:theme="@style/SwitchCompatTheme" />

                        </RelativeLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.1dp"
                            android:background="@color/view_setting" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="gone"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/txt_change_language"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:layout_weight="0.4"
                                android:fontFamily="@font/public_regular"
                                android:paddingTop="15dp"
                                android:paddingBottom="15dp"
                                android:text="@string/change_language"
                                android:textColor="@color/font_dark"
                                android:textSize="16sp" />

                            <Spinner
                                android:id="@+id/spinner"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_vertical"
                                android:layout_marginEnd="5dp"
                                android:layout_weight="0.6"
                                android:backgroundTint="@color/colorPrimary" />

                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.1dp"
                            android:visibility="gone"
                            android:background="@color/view_setting" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:paddingTop="10dp"
                            android:paddingBottom="10dp">

                            <LinearLayout
                                android:id="@+id/linearLayout_clear_setting"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:fontFamily="@font/public_regular"
                                    android:text="@string/clear_cache"
                                    android:textColor="@color/font_dark"
                                    android:textStyle="bold" />

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="2dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:fontFamily="@font/public_regular"
                                        android:text="@string/locally_cached_data"
                                        android:textColor="@color/font_dark"
                                        android:textSize="12sp" />

                                    <TextView
                                        android:id="@+id/textView_size_setting"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="3dp"
                                        android:fontFamily="@font/public_regular"
                                        android:text=""
                                        android:textColor="@color/text_black"
                                        android:textSize="12sp" />

                                </LinearLayout>

                            </LinearLayout>

                            <ImageView
                                android:id="@+id/iv_clear"
                                android:layout_width="40dp"
                                android:layout_height="30dp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:layout_marginEnd="10dp"
                                android:contentDescription="@string/app_name"
                                android:src="@drawable/ic_brush" />

                        </RelativeLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.1dp"
                            android:background="@color/view_setting" />

                        <TextView
                            android:id="@+id/txt_privacy_policy"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/public_regular"
                            android:paddingTop="15dp"
                            android:paddingBottom="15dp"
                            android:text="@string/Privacy_policy"
                            android:textColor="@color/font_dark"
                            android:textSize="16sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.1dp"
                            android:background="@color/view_setting" />

                        <TextView
                            android:id="@+id/txt_about_us"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/public_regular"
                            android:paddingTop="15dp"
                            android:paddingBottom="15dp"
                            android:text="@string/about_us"
                            android:textColor="@color/font_dark"
                            android:textSize="16sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.1dp"
                            android:background="@color/view_setting" />


                        <TextView
                            android:id="@+id/txt_share_app"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/public_regular"
                            android:paddingTop="15dp"
                            android:paddingBottom="15dp"
                            android:text="@string/share_app"
                            android:textColor="@color/font_dark"
                            android:textSize="16sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.1dp"
                            android:background="@color/view_setting" />

                        <TextView
                            android:id="@+id/txt_rate_app"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/public_regular"
                            android:paddingTop="15dp"
                            android:paddingBottom="15dp"
                            android:text="@string/rate_app"
                            android:textColor="@color/font_dark"
                            android:textSize="16sp" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.1dp"
                            android:background="@color/view_setting" />

                        <TextView
                            android:id="@+id/txt_login"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:fontFamily="@font/public_regular"
                            android:paddingTop="15dp"
                            android:paddingBottom="15dp"
                            android:text="@string/login"
                            android:textColor="@color/font_dark"
                            android:textSize="16sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="15dp" />
            </LinearLayout>
        </ScrollView>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>