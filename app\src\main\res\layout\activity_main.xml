<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:openDrawer="start">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/al_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/AppTheme.AppBarOverlay"
            tools:ignore="MissingConstraints">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="70dp"
                android:background="?attr/colorPrimary"
                app:layout_scrollFlags="scroll|enterAlways">

                <LinearLayout
                    android:id="@+id/ly_search"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:weightSum="1.0">

                    <TextView
                        android:id="@+id/txt_title"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:fontFamily="@font/public_medium"
                        android:gravity="center"
                        android:text="Video Status"
                        android:textColor="@color/font_dark"
                        android:textSize="18dp"
                        android:visibility="visible" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:visibility="gone"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/txt_points"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="00"
                            android:textColor="@color/colorPrimary"
                            android:textSize="16dp"
                            android:visibility="visible" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:layout_marginTop="3dp"
                            android:gravity="center|right"
                            android:text=" PTS"
                            android:textColor="@color/font_dark"
                            android:textSize="12dp"
                            android:visibility="visible" />
                    </LinearLayout>

                </LinearLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <include layout="@layout/content_main" />

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
