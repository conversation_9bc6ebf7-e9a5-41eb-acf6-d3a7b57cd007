package com.videostatus.Activity;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.videostatus.Adapter.NewArrival_Adapter;
import com.videostatus.Model.VideoModel.Result;
import com.videostatus.Model.VideoModel.VideoModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.Interface.ShareAll;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.util.ArrayList;
import java.util.List;

import ir.alirezabdn.wp7progress.WP10ProgressBar;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class NewArrival extends AppCompatActivity implements ShareAll {

    public NewArrival() {
    }

    WP10ProgressBar progressBar;
    RecyclerView recycler_newarrival;
    List<Result> NewArrivalList;
    NewArrival_Adapter newArrival_adapter;

    PrefManager prefManager;
    Toolbar toolbar;
    LinearLayout ly_back;
    TextView toolbar_title;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.newarrival);

        Toolbar toolbar = (Toolbar) findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        PrefManager.forceRTLIfSupported(getWindow(), NewArrival.this);
        prefManager = new PrefManager(NewArrival.this);

        toolbar_title = findViewById(R.id.toolbar_title);
        toolbar_title.setText(getResources().getString(R.string.new_arrival));
        progressBar = findViewById(R.id.wp7progressBar);

        recycler_newarrival = findViewById(R.id.recycler_newarrival);

        ly_back = findViewById(R.id.ly_back);
        ly_back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        NewArrival();

    }

    private void NewArrival() {

        progressBar.showProgressBar();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<VideoModel> call = bookNPlayAPI.new_arrival();
        call.enqueue(new Callback<VideoModel>() {
            @Override
            public void onResponse(Call<VideoModel> call, Response<VideoModel> response) {
                progressBar.hideProgressBar();
                if (response.code() == 200) {
                    NewArrivalList = new ArrayList<>();
                    NewArrivalList = response.body().getResult();
                    Log.e("NewArrivalList", "" + NewArrivalList.size());

                    newArrival_adapter = new NewArrival_Adapter(NewArrival.this, NewArrivalList, NewArrival.this);
                    GridLayoutManager gridLayoutManager = new GridLayoutManager(getApplicationContext(), 2);
                    recycler_newarrival.setLayoutManager(gridLayoutManager);
                    recycler_newarrival.setItemAnimator(new DefaultItemAnimator());
                    recycler_newarrival.setAdapter(newArrival_adapter);
                    newArrival_adapter.notifyDataSetChanged();

                }
            }

            @Override
            public void onFailure(Call<VideoModel> call, Throwable t) {
                progressBar.hideProgressBar();
            }
        });
    }

    @Override
    public void WhatsappShare() {

    }
}
