package com.videostatus.Activity.VideoRecord;

import android.app.ProgressDialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import androidx.appcompat.app.AppCompatActivity;
import com.videostatus.R;
import com.videostatus.Utility.Constant;
import com.videostatus.Utility.Utils;
import java.io.File;
import life.knowledge4.videotrimmer.K4LVideoTrimmer;
import life.knowledge4.videotrimmer.interfaces.OnTrimVideoListener;

public class TrimVideo extends AppCompatActivity implements OnTrimVideoListener {

    private K4LVideoTrimmer mVideoTrimmer;
    private ProgressDialog mProgressDialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.trimvideo);

        Intent extraIntent = getIntent();
        String path = "";

        if (extraIntent != null) {
            path = extraIntent.getStringExtra("path");
        }

        //setting progressbar
        mProgressDialog = new ProgressDialog(this);
        mProgressDialog.setCancelable(false);
        mProgressDialog.setMessage("Please wait...");

        mVideoTrimmer = ((K4LVideoTrimmer) findViewById(R.id.timeLine));
        if (mVideoTrimmer != null) {
            mVideoTrimmer.setMaxDuration((Constant.max_recording_duration / 1000));
            mVideoTrimmer.setOnTrimVideoListener(this);
            mVideoTrimmer.setDestinationPath(Constant.app_showing_folder);
            mVideoTrimmer.setVideoURI(Uri.parse(path));
        }
        Log.e(Constant.tag + " orignal path", path);

    }


    @Override
    public void onTrimStarted() {
        mProgressDialog.show();
    }

    @Override
    public void getResult(final Uri uri) {
        mProgressDialog.dismiss();
        try {
            File video_file = new File(uri.getPath());
            Log.d(Constant.tag + " new path", video_file.getAbsolutePath());
            Utils.copyFile(video_file,
                    new File(Constant.gallery_resize_video));

            if (video_file.exists()) {
                video_file.delete();
            }

            Intent intent = new Intent(TrimVideo.this, GallaryActivity.class);
            intent.putExtra("video_path", Constant.gallery_resize_video);
            startActivity(intent);

            mVideoTrimmer.destroy();
            finish();

        } catch (Exception e) {
            e.printStackTrace();
            Log.e(Constant.tag, e.toString());
        }

    }

    @Override
    public void cancelAction() {
        mProgressDialog.cancel();
        mVideoTrimmer.destroy();
        finish();
    }

    @Override
    public void onError(String message) {
        mProgressDialog.dismiss();
    }

}
