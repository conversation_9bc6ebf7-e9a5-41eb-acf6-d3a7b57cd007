<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_select"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="10dp"
    android:layout_marginEnd="10dp"
    tools:background="@color/black">

    <jp.co.cyberagent.android.gpuimage.GPUImageView
        android:id="@+id/iv_photo"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:padding="3dp"
        app:gpuimage_show_loading="false"
        app:gpuimage_surface_type="texture_view"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="MissingConstraints" />

    <TextView
        android:id="@+id/filter_txt"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:maxLines="2"
        android:paddingTop="2dp"
        android:textColor="@color/white"
        android:textSize="10sp"
        app:layout_constraintTop_toBottomOf="@id/iv_photo"
        tools:ignore="MissingConstraints"
        tools:text="Filter name" />

</androidx.constraintlayout.widget.ConstraintLayout>