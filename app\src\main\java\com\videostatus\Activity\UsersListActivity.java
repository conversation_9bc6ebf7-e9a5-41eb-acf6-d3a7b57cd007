package com.videostatus.Activity;

import android.app.Fragment;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.videostatus.Adapter.UserAdapter;
import com.videostatus.Model.UserModel.Result;
import com.videostatus.Model.UserModel.UserModel;
import com.videostatus.R;
import com.videostatus.Utility.PrefManager;
import com.videostatus.WebService.BaseURL;
import com.videostatus.WebService.VideoStatusAPI;

import java.util.ArrayList;
import java.util.List;

import ir.alirezabdn.wp7progress.WP10ProgressBar;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import static android.content.Context.MODE_PRIVATE;

public class UsersListActivity extends AppCompatActivity {

    public UsersListActivity() {
    }

    WP10ProgressBar progressBar;
    RecyclerView recycler_user;
    UserAdapter userAdapter;
    List<Result> UserList;
    String id;

    PrefManager prefManager;
    Toolbar toolbar;
    LinearLayout ly_back;
    TextView toolbar_title;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.userlist_activity);

        PrefManager.forceRTLIfSupported(getWindow(), UsersListActivity.this);
        prefManager = new PrefManager(UsersListActivity.this);

        toolbar = findViewById(R.id.toolbar);

        toolbar_title = findViewById(R.id.toolbar_title);
        toolbar_title.setText(getResources().getString(R.string.userslist));

        progressBar = findViewById(R.id.wp7progressBar);
        recycler_user = findViewById(R.id.recycler_userlist);

        ly_back=findViewById(R.id.ly_back);
        ly_back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        top_users();

    }

    private void top_users() {

        progressBar.showProgressBar();
        VideoStatusAPI bookNPlayAPI = BaseURL.getVideoAPI();
        Call<UserModel> call = bookNPlayAPI.top_users();
        call.enqueue(new Callback<UserModel>() {
            @Override
            public void onResponse(Call<UserModel> call, Response<UserModel> response) {
                progressBar.hideProgressBar();
                if (response.code() == 200) {
                    UserList = new ArrayList<>();
                    UserList = response.body().getResult();
                    Log.e("UserList", "" + UserList.size());

                    userAdapter = new UserAdapter(UsersListActivity.this, UserList);
                    GridLayoutManager manager = new GridLayoutManager(UsersListActivity.this, 2, GridLayoutManager.VERTICAL, false);
                    recycler_user.setLayoutManager(manager);
                    recycler_user.setItemAnimator(new DefaultItemAnimator());
                    recycler_user.setAdapter(userAdapter);
                }
            }

            @Override
            public void onFailure(Call<UserModel> call, Throwable t) {
                progressBar.hideProgressBar();
            }
        });
    }
}
