package com.videostatus.Utility;

import android.content.SharedPreferences;
import android.os.Environment;

import com.videostatus.Model.VideoModel.Result;

import java.util.ArrayList;
import java.util.List;

public class Constant {
    public static final String PURCHASED_CODE = "xxxx-xxxx-xxxx-xxxx";

    public static String root = Environment.getExternalStorageDirectory().toString();

    public static String tag = "VideoStatus";

    public static String app_folder = root + "/VideoStatus/";
    public static String app_folder1 = "VideoStatus";

    public static final String app_showing_folder =root+"/VideoStatus/";
    public static final String draft_app_folder= app_showing_folder +"Draft/";

    public static String SelectedAudio_MP3 = "SelectedAudio.mp3";
    public static String SelectedAudio_AAC = "SelectedAudio.aac";

    public static String outputfile = root + "/output.mp4";
    public static String outputfile2 = root + "/output2.mp4";
    public static String output_filter_file = root + "/output-filtered.mp4";

    public static String output_frontcamera= app_showing_folder + "output_frontcamera.mp4";

    public static String Selected_sound_id = "null";

    public static int recording_duration = 18000;
    public static int max_recording_duration = 18000;
    public static int min_time_recording=5000;

    public final static int permission_camera_code=786;
    public final static int permission_write_data=788;
    public final static int permission_Read_data=789;
    public final static int permission_Recording_audio=790;
    public final static int Pick_video_from_gallery=791;

    public static String gallery_trimed_video=app_folder + "gallery_trimed_video.mp4";
    public static String gallery_resize_video=app_folder + "gallery_resize_video.mp4";

    public static int screen_width;
    public static int screen_height;

    public static SharedPreferences sharedPreferences;
    public static final String pref_name="pref_name";
    public static final String u_id="u_id";
    public static final String u_name="u_name";
    public static final String u_pic="u_pic";
    public static final String f_name="f_name";
    public static final String l_name="l_name";
    public static final String gender="u_gender";
    public static final String islogin="is_login";
    public static final String device_token="device_token";
    public static final String api_token="api_token";
    public static final String device_id="device_id";
    public static final String uploading_video_thumb="uploading_video_thumb";


    //Minimum Video you want to buffer while Playing
    public static final int MIN_BUFFER_DURATION = 25000;
    //Max Video you want to buffer during PlayBack
    public static final int MAX_BUFFER_DURATION = 30000;
    //Min Video you want to buffer before start Playing it
    public static final int MIN_PLAYBACK_START_BUFFER = 10000;
    //Min video You want to buffer when user resumes video
    public static final int MIN_PLAYBACK_RESUME_BUFFER = 10000;
    //Video Url
    public static final String VIDEO_URL = "http://www.sample-videos.com/video/mp4/720/big_buck_bunny_720p_30mb.mp4";

    public static List<Result> VideoList=new ArrayList<>();

    public static int is_follow_related=0;

}
