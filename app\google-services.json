{"project_info": {"project_number": "476849697961", "firebase_url": "https://myallapp-f9c13.firebaseio.com", "project_id": "myallapp-f9c13", "storage_bucket": "myallapp-f9c13.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:476849697961:android:e4fcb27fea6b7298", "android_client_info": {"package_name": "com.priyastack.bhajanvideo"}}, "oauth_client": [{"client_id": "476849697961-sjjnahcbtqvqfp0j9jnikbupgciv9uom.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.priyastack.bhajanvideo", "certificate_hash": "a9ad821a2c01b8996f4dfdec631809c5769072d7"}}, {"client_id": "476849697961-s1a5bl0mat7ps4t4efbni95lc96i7641.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBZLp0GKlwhrFBjRIEELxCvyVGj49_1VYw"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 2, "other_platform_oauth_client": [{"client_id": "476849697961-s1a5bl0mat7ps4t4efbni95lc96i7641.apps.googleusercontent.com", "client_type": 3}]}, "ads_service": {"status": 2}}}, {"client_info": {"mobilesdk_app_id": "1:476849697961:android:61dec69222747b4d", "android_client_info": {"package_name": "com.priyastack.dailyqoute"}}, "oauth_client": [{"client_id": "476849697961-aj4d3348rqju6828ed81l1dlqho4a0ro.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.priyastack.dailyqoute", "certificate_hash": "a9ad821a2c01b8996f4dfdec631809c5769072d7"}}, {"client_id": "476849697961-s1a5bl0mat7ps4t4efbni95lc96i7641.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBZLp0GKlwhrFBjRIEELxCvyVGj49_1VYw"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 2, "other_platform_oauth_client": [{"client_id": "476849697961-s1a5bl0mat7ps4t4efbni95lc96i7641.apps.googleusercontent.com", "client_type": 3}]}, "ads_service": {"status": 2}}}, {"client_info": {"mobilesdk_app_id": "1:476849697961:android:145aa9339976e293", "android_client_info": {"package_name": "com.priyastack.livetv"}}, "oauth_client": [{"client_id": "476849697961-s1a5bl0mat7ps4t4efbni95lc96i7641.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBZLp0GKlwhrFBjRIEELxCvyVGj49_1VYw"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 1, "other_platform_oauth_client": []}, "ads_service": {"status": 2}}}, {"client_info": {"mobilesdk_app_id": "1:476849697961:android:a4812dc3eae8beed", "android_client_info": {"package_name": "com.videostatus"}}, "oauth_client": [{"client_id": "476849697961-phieddllujdccdiho9ic2f9nl59b1vc3.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.videostatus", "certificate_hash": "a9ad821a2c01b8996f4dfdec631809c5769072d7"}}, {"client_id": "476849697961-s1a5bl0mat7ps4t4efbni95lc96i7641.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBZLp0GKlwhrFBjRIEELxCvyVGj49_1VYw"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 2, "other_platform_oauth_client": [{"client_id": "476849697961-s1a5bl0mat7ps4t4efbni95lc96i7641.apps.googleusercontent.com", "client_type": 3}]}, "ads_service": {"status": 2}}}], "configuration_version": "1"}