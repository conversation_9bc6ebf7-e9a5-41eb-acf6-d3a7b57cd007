<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="navigation_view">

    <group android:checkableBehavior="single">
        <item
            android:id="@+id/nav_Home"
            android:icon="@drawable/ic_home"
            android:title="Home" />
        <item
            android:id="@+id/navigation_category"
            android:icon="@drawable/ic_category"
            android:title="Category" />
        <item
            android:id="@+id/navigation_earn"
            android:icon="@drawable/ic_rewards"
            android:title="@string/earn_point" />

        <item
            android:id="@+id/nav_upload"
            android:icon="@drawable/ic_server"
            android:title="Upload Status" />

    </group>
    <item android:title="Communication">
        <menu>
            <item
                android:id="@+id/nav_share"
                android:icon="@drawable/ic_menu_share"
                android:title="Share App" />

            <itemw
                android:id="@+id/nav_about"
                android:icon="@drawable/about_us"
                android:title="About Us" />

            <item
                android:id="@+id/nav_rate"
                android:icon="@drawable/rate"
                android:title="Rate App" />
            <item
                android:id="@+id/nav_logout"
                android:icon="@drawable/exit"
                android:visible="false"
                android:title="Logout" />

        </menu>
    </item>

</menu>
